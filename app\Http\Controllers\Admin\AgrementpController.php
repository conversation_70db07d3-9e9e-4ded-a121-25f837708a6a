<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Agrementp;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;

class AgrementpController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        // 1. Handle AJAX calls only when it *is* an AJAX request
        if ($request->ajax()) {

            /**
             * 2.  Let DataTables work with an Eloquent *builder* rather than a
             *    fully‑hydrated collection.  It’s faster and allows server‑side
             *    searching / ordering automatically.
             */
            $query = Agrementp::select(['id', 'compte_id', 'produit', 'numero', 'created_at']);

            return DataTables::of($query)      // ← plural “DataTables”, not “DataTable”

            ->addColumn('compte', function (Agrementp $Agrementp) {
                // Check if 'controlleur' relationship exists before accessing 'nom'
                return $Agrementp->compte ? $Agrementp->compte->name : '';
            })
            ->addColumn('actions', function (Agrementp $agrementp) {
                // 3.  You can keep the inline HTML, or (cleaner) move it to a Blade partial
                return '
                    <a href="'.route('agrement_produit.show', $agrementp->id).'" class="btn btn-sm btn-icon btn-bg-light btn-active-color-primary">
                <!--begin::Svg Icon | path: icons/duotune/arrows/arr064.svg-->
                <span class="svg-icon svg-icon-2">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <rect opacity="0.5" x="18" y="13" width="13" height="2" rx="1" transform="rotate(-180 18 13)" fill="currentColor"></rect>
                        <path d="M15.4343 12.5657L11.25 16.75C10.8358 17.1642 10.8358 17.8358 11.25 18.25C11.6642 18.6642 12.3358 18.6642 12.75 18.25L18.2929 12.7071C18.6834 12.3166 18.6834 11.6834 18.2929 11.2929L12.75 5.75C12.3358 5.33579 11.6642 5.33579 11.25 5.75C10.8358 6.16421 10.8358 6.83579 11.25 7.25L15.4343 11.4343C15.7467 11.7467 15.7467 12.2533 15.4343 12.5657Z" fill="currentColor"></path>
                    </svg>
                </span>
                <!--end::Svg Icon-->
                               </a>';
            })
                ->rawColumns(['actions'])      // 4.  Let DataTables know “actions” is raw HTML
                ->make(true);                  // 5.  Use ->make(true) (or ->toJson()) either is fine
        }
        // 6.  Non‑AJAX: just render the page
        return view('admin.Agrementp.index');
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        return view('admin.Agrementp.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $record = new Agrementp();
        $record->compte_id = $request ->compte_id;
        $record->numero = $request ->numero;
        $record->produit = $request ->produit;
        $record->save();
        return redirect()->route('agrement_produit.show',$record->id);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $record = Agrementp::find($id);
        return view('admin.Agrementp.show',compact('record'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $agrementProduit = \App\Models\Agrementp::findOrFail($id);
        $comptes = \App\Models\Compte::all();

        return view('admin.Agrementp.edit', compact('agrementProduit', 'comptes'));
    }


    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        // ✅ Validation des données
        $validated = $request->validate([
            'compte_id' => 'required|integer|exists:comptes,id',
            'numero' => 'required|string|max:255',
            'produit' => 'required|string|max:255',
        ]);

        // ✅ Récupération de l'enregistrement existant
        $record = \App\Models\Agrementp::findOrFail($id);

        // ✅ Mise à jour des champs
        $record->compte_id = $request->compte_id;
        $record->numero = $request->numero;
        $record->produit = $request->produit;
        $record->save();

        // ✅ Redirection
        return redirect()->route('agrement_produit.show', $record->id)
            ->with('success', 'Agrément produit mis à jour avec succès.');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        try {
            $record = \App\Models\Agrementp::findOrFail($id);
            $record->delete();

            return redirect()->route('agrement_produit.index')
                ->with('success', 'Agrément produit supprimé avec succès.');
        } catch (\Exception $e) {
            return back()->withErrors([
                'error' => 'Une erreur est survenue lors de la suppression : ' . $e->getMessage()
            ]);
        }
    }




    public function print($id)
    {
        $agrementp = Agrementp::findOrFail($id);
        return view('admin.Agrementp.print', compact('agrementp'));

    }
}
