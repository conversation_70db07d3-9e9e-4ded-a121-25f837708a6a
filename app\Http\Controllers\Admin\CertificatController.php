<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Certificat;
use App\Models\Compte;
use App\Models\Controlleur;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;


class CertificatController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            $query = Certificat::with(['compte', 'controlleur'])
                ->select(['id', 'compte_id', 'controlleur_id', 'produit', 'poids', 'quantite', 'date_creation', 'date_expiration']);

            return DataTables::of($query)
                ->addColumn('compte', function (Certificat $certificat) {
                    return $certificat->compte ? $certificat->compte->name : '';
                })
                ->addColumn('controlleur', function (Certificat $certificat) {
                    return $certificat->controlleur ? $certificat->controlleur->nom : '';
                })
                ->addColumn('actions', function (Certificat $certificat) {
                    return '
                    <a href="' . route('sertificats.show', $certificat->id) . '" class="btn btn-sm btn-icon btn-bg-light btn-active-color-primary">
                        <i class="fas fa-eye"></i>
                    </a>';
                })
                ->rawColumns(['actions'])
                ->make(true);
        }

        return view('admin.Certificats.index');
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        return view('admin.Certificats.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {

        try {
            // ✅ Validation des données
            $validated = $request->validate([
                'compte_id' => 'nullable|string|max:255',
                'controlleur_id' => 'nullable|string|max:255',
                'produit' => 'required|string|max:255',
                'poids' => 'required|string|max:255',
                'num_lot' => 'nullable|string|max:255',
                'conditionnement' => 'nullable|string|max:255',
                'dure_fabrication' => 'nullable|string|max:255',
                'quantite' => 'required|string|max:255',
                'date_creation' => 'nullable|string',
                'date_expiration' => 'nullable|string',
                'auditeur' => 'nullable|string|max:255',
                'adresse_auditeur' => 'nullable|string|max:255',
            ]);

            // ✅ Création du modèle manuellement avec formatage des dates
            $certificat = new Certificat();
            $certificat->compte_id = $request->input('compte_id');
            $certificat->controlleur_id = $request->input('controlleur_id');
            $certificat->produit = $request->input('produit');
            $certificat->poids = $request->input('poids');
            $certificat->numero = $request->input('numero');
            $certificat->num_lot = $request->input('num_lot');
            $certificat->conditionnement = $request->input('conditionnement');
            $certificat->dure_fabrication = $request->input('dure_fabrication');
            $certificat->quantite = $request->input('quantite');
            $certificat->date_creation = $request->input('date_creation')
                ? Carbon::createFromFormat('d/m/Y', $request->input('date_creation'))->format('Y-m-d')
                : null;
            $certificat->date_expiration = $request->input('date_expiration')
                ? Carbon::createFromFormat('d/m/Y', $request->input('date_expiration'))->format('Y-m-d')
                : null;
            $certificat->auditeur = $request->input('auditeur');
            $certificat->adresse_auditeur = $request->input('adresse_auditeur');

            $certificat->save();

            return redirect()->route('sertificats.index')->with('success', 'Certificat ajouté avec succès.');
        } catch (\Exception $e) {
            return back()->withInput()->withErrors([
                'error' => 'Une erreur est survenue : ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        // Récupérer le certificat avec ses relations
        $record = Certificat::with(['compte', 'controlleur'])->findOrFail($id);

        // Retourner la vue avec le certificat
        return view('admin.Certificats.show', compact('record'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        // Récupération du certificat par son ID
        $certificat = Certificat::with('compte')->findOrFail($id);

        // (Optionnel) Récupérer des données supplémentaires pour le formulaire (ex : comptes, catégories, etc.)
        $comptes = Compte::all();
        $controlleurs = Controlleur::all();

        // Retourner la vue d'édition avec les données
        return view('admin.Certificats.edit', compact('certificat', 'comptes','controlleurs'));
    }


    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        try {
            // ✅ Validation des données
            $validated = $request->validate([
                'compte_id' => 'nullable|string|max:255',
                'controlleur_id' => 'nullable|string|max:255',
                'produit' => 'required|string|max:255',
                'poids' => 'required|string|max:255',
                'num_lot' => 'nullable|string|max:255',
                'conditionnement' => 'nullable|string|max:255',
                'dure_fabrication' => 'nullable|string|max:255',
                'quantite' => 'required|string|max:255',
                'date_creation' => 'nullable|string',
                'date_expiration' => 'nullable|string',
                'auditeur' => 'nullable|string|max:255',
                'adresse_auditeur' => 'nullable|string|max:255',
            ]);

            // ✅ Recherche du certificat existant
            $certificat = Certificat::findOrFail($id);

            // ✅ Mise à jour des champs
            $certificat->compte_id = $request->input('compte_id');
            $certificat->controlleur_id = $request->input('controlleur_id');
            $certificat->produit = $request->input('produit');
            $certificat->poids = $request->input('poids');
            $certificat->numero = $request->input('numero');
            $certificat->num_lot = $request->input('num_lot');
            $certificat->conditionnement = $request->input('conditionnement');
            $certificat->dure_fabrication = $request->input('dure_fabrication');
            $certificat->quantite = $request->input('quantite');
            $certificat->date_creation = $request->input('date_creation')
                ? Carbon::createFromFormat('d/m/Y', $request->input('date_creation'))->format('Y-m-d')
                : null;
            $certificat->date_expiration = $request->input('date_expiration')
                ? Carbon::createFromFormat('d/m/Y', $request->input('date_expiration'))->format('Y-m-d')
                : null;
            $certificat->auditeur = $request->input('auditeur');
            $certificat->adresse_auditeur = $request->input('adresse_auditeur');

            $certificat->save();

            return redirect()->route('sertificats.index')->with('success', 'Certificat mis à jour avec succès.');

        } catch (\Exception $e) {
            return back()->withInput()->withErrors([
                'error' => 'Une erreur est survenue : ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        try {
            $certificat = \App\Models\Certificat::findOrFail($id);
            $certificat->delete();

            return redirect()->route('sertificats.index')->with('success', 'Certificat supprimé avec succès.');
        } catch (\Exception $e) {
            return back()->withErrors([
                'error' => 'Une erreur est survenue lors de la suppression : ' . $e->getMessage()
            ]);
        }
    }



    public function print($id)
    {
        $agrementp = Certificat::findOrFail($id);
        return view('admin.Certificats.print', compact('agrementp'));

    }
}
