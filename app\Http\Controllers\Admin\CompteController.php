<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Compte;
use App\Models\Controlleur;
use Illuminate\Http\Request;
use Yajra\DataTables\DataTables;

class CompteController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            $comptes = Compte::all();
            return DataTables::of($comptes)
                ->addColumn('actions', function (Compte $comptes) {
                    // Generate action button HTML with route
                    return '<a href="'.route('comptes.show', $comptes->id).'" class="btn btn-sm btn-icon btn-bg-light btn-active-color-primary">
                                <!--begin::Svg Icon | path: icons/duotune/arrows/arr064.svg-->
                                <span class="svg-icon svg-icon-2">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                        <rect opacity="0.5" x="18" y="13" width="13" height="2" rx="1" transform="rotate(-180 18 13)" fill="currentColor"></rect>
                                        <path d="M15.4343 12.5657L11.25 16.75C10.8358 17.1642 10.8358 17.8358 11.25 18.25C11.6642 18.6642 12.3358 18.6642 12.75 18.25L18.2929 12.7071C18.6834 12.3166 18.6834 11.6834 18.2929 11.2929L12.75 5.75C12.3358 5.33579 11.6642 5.33579 11.25 5.75C10.8358 6.16421 10.8358 6.83579 11.25 7.25L15.4343 11.4343C15.7467 11.7467 15.7467 12.2533 15.4343 12.5657Z" fill="currentColor"></path>
                                    </svg>
                                </span>
                                <!--end::Svg Icon-->
                             </a>';
                })
                ->rawColumns(['actions'])
                ->toJson();
        }
        return view('admin.compte.index');
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $record = Compte::find($id);
        return view('admin.compte.show',compact('record'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $record = Compte::find($id);
        return view('admin.compte.edit',compact('record'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        /**
        // Step 1: Validation
        $request->validate([
            'name' => 'required|string|max:255',
            'categoriecompte_id' => 'required|string|max:255',
            'telephone' => 'nullable|string|max:255',
            'fax' => 'nullable|string|max:255',
            'email' => 'nullable|email|max:255',
            'site_web' => 'nullable|string|max:255',
            'adresse' => 'nullable|string|max:255',
            'code_postal' => 'nullable|string|max:255',
            'ville' => 'nullable|string|max:255',
            'region' => 'nullable|string|max:255',
            'pays' => 'nullable|string|max:255',
            'tva' => 'nullable|string|max:255',
            'siret' => 'nullable|string|max:255',
            'agrement' => 'nullable|string|max:255',
            'user_id' => 'nullable|integer',
            'image' => 'nullable|string|max:255',
            'share_info' => 'required|boolean',
            'approved' => 'required|boolean',
        ]);
         * */

        // Step 2: Find the Compte
        $compte = Compte::findOrFail($id);

        // Step 3: Assign Request Data to Compte Attributes
        $compte->name = $request->name;
        //$compte->categoriecompte_id = $request->categoriecompte_id;
        $compte->telephone = $request->telephone;
        $compte->fax = $request->fax;
        $compte->email = $request->email;
        $compte->site_web = $request->site_web;
        $compte->adresse = $request->adresse;
        $compte->code_postal = $request->code_postal;
        $compte->ville = $request->ville;
        $compte->region = $request->region;
        $compte->pays = $request->pays;
        $compte->tva = $request->tva;
        $compte->siret = $request->siret;
        $compte->agrement = $request->agrement;
        //$compte->user_id = $request->user_id;
        if ($request->hasFile('image')) {
            $image = $request->file('image');
            $imageName = time() . '.' . $image->getClientOriginalExtension();
            $image->move(public_path('upload/compte'), $imageName);

            // Mettre à jour le chemin de l'image du produit
            $compte->image = 'upload/compte/' . $imageName;
            //$record->save();
        }
        //$compte->share_info = $request->share_info;
        //$compte->approved = $request->approved;

        // Step 4: Save the Compte
        $compte->save();

        return redirect()->route('comptes.show',$compte->id);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        // Find the compte by ID
        $compte = Compte::findOrFail($id);

        // Delete the compte
        $compte->delete();

        // Return a success response
        return redirect()->route('comptes.index');
    }


    public function toggleApproved($id)
    {
        $record = Compte::findOrFail($id);
        $record->approved = !$record->approved;
        $record->save();

        return response()->json(['success' => true, 'approved' => $record->approved]);
    }
}
