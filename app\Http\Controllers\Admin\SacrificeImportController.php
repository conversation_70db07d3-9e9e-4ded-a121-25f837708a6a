<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Sacrifice;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\Session;

class SacrificeImportController extends Controller
{
    public function showForm()
    {
        return view('admin.Sacrifice.import.upload');
    }

    public function upload(Request $request)
    {
        $request->validate([
            'file' => 'required|file|mimes:xlsx,csv',
        ]);

        $path = $request->file('file')->store('temp');
        $data = Excel::toArray([], storage_path("app/$path"));

        Session::put('import_sacrifice_path', $path);
        Session::put('import_sacrifice_data', $data[0]);

        return view('admin.Sacrifice.import.preview', ['rows' => $data[0]]);
    }

    public function importConfirm(Request $request)
    {
        $rows = Session::get('import_sacrifice_data');

        foreach ($rows as $index => $row) {
            if ($index === 0) continue; // skip headers

            Sacrifice::create([
                'reference' => $row[0],
                'controlleur_id' => $row[1],
                'societe_nettoyage_id' => $row[2],
                'organisme_control_id' => $row[3],
                'abattoire_id' => $row[4],
                'compte_id' => $row[5],
                'date_sacrifice' => $row[6],
                'user_id' => $row[7],
                'approved' => $row[8] ?? 0,
                'reference_bete' => $row[9],
                'image' => $row[10],
                'categorie' => $row[11],
                'classement' => $row[12],
                'race_bete' => $row[13],
                'age' => $row[14],
                'poids' => $row[15],
                'date_arrive' => $row[16],
                'num_lot_sacrifice' => $row[17],
                'num_lot_elevage' => $row[18],
            ]);
        }

        Session::forget(['import_sacrifice_data', 'import_sacrifice_path']);
        return redirect()->route('sacrifices.index')->with('success', 'Importation réussie !');
    }
}
