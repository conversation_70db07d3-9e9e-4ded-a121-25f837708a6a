<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Atelierconditionnement extends Model
{
    use HasFactory;

    use SoftDeletes;

    protected $fillable = [
        'name', 'tva', 'siret', 'telephone', 'email', 'adresse', 'ville',
        'pays', 'website', 'image', 'approved'
    ];
}
