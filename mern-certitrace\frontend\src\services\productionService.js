import { apiGet, apiPost, apiPut, apiDelete, apiDownload } from './api'

const productionService = {
  // Get all productions
  getAll: async (params = {}) => {
    const queryString = new URLSearchParams(params).toString()
    const response = await apiGet(`/productions?${queryString}`)
    return response
  },

  // Get production by ID
  getById: async (id) => {
    const response = await apiGet(`/productions/${id}`)
    return response
  },

  // Create new production
  create: async (productionData) => {
    const response = await apiPost('/productions', productionData)
    return response
  },

  // Update production
  update: async (id, productionData) => {
    const response = await apiPut(`/productions/${id}`, productionData)
    return response
  },

  // Delete production
  delete: async (id) => {
    const response = await apiDelete(`/productions/${id}`)
    return response
  },

  // Approve production
  approve: async (id) => {
    const response = await apiPost(`/productions/${id}/approve`)
    return response
  },

  // Generate certificate
  generateCertificate: async (id) => {
    const response = await apiGet(`/productions/${id}/certificate`)
    return response
  },

  // Download certificate PDF
  downloadCertificate: async (id, filename = 'production-certificate.pdf') => {
    await apiDownload(`/productions/${id}/certificate/pdf`, filename)
  },

  // Duplicate production
  duplicate: async (id) => {
    const response = await apiPost(`/productions/${id}/duplicate`)
    return response
  },

  // Get production statistics
  getStats: async (params = {}) => {
    const queryString = new URLSearchParams(params).toString()
    const response = await apiGet(`/productions/stats?${queryString}`)
    return response
  },
}

export default productionService
