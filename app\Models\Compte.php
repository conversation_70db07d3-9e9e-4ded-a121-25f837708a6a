<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Compte extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $fillable = [
        'name', 'categoriecompte_id', 'telephone', 'fax', 'email', 'site_web',
        'adresse', 'code_postal', 'ville', 'region', 'pays', 'tva', 'siret',
        'agrement', 'user_id', 'image', 'share_info', 'approved'
    ];

    public function contacts()
    {
        return $this->hasMany(Contact::class);
    }


    public function production(){
        return $this->hasMany(Production::class);
    }

    public function categoriecompte(){
        return $this->belongsTo(Categoriecompte::class);
    }

    public function user(){
        return $this->belongsTo(User::class);
    }
}
