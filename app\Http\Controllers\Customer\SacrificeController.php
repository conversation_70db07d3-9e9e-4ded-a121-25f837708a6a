<?php

namespace App\Http\Controllers\Customer;

use App\Http\Controllers\Controller;
use App\Models\Sacrifice;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class SacrificeController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            $data = Sacrifice::where('compte_id',Auth::user()->compte_id)->get();

            return datatables()->of($data)
                ->addColumn('approved', function (Sacrifice $sacrifice) {
                    // Set badge text based on the value of 'approved'
                    $badge = $sacrifice->approved == 1 ? '<span class="badge badge-light-success">Approuvé</span>' : '<span class="badge badge-light-danger">Non Approuvé</span>';
                    return $badge;
                })
                ->addColumn('actions', function (Sacrifice $sacrifice) {
                    // Generate action button HTML with route
                    return '<a href="'.route('sacrifice.show', $sacrifice->id).'" class="btn btn-sm btn-icon btn-bg-light btn-active-color-primary">
                <!--begin::Svg Icon | path: icons/duotune/arrows/arr064.svg-->
                <span class="svg-icon svg-icon-2">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <rect opacity="0.5" x="18" y="13" width="13" height="2" rx="1" transform="rotate(-180 18 13)" fill="currentColor"></rect>
                        <path d="M15.4343 12.5657L11.25 16.75C10.8358 17.1642 10.8358 17.8358 11.25 18.25C11.6642 18.6642 12.3358 18.6642 12.75 18.25L18.2929 12.7071C18.6834 12.3166 18.6834 11.6834 18.2929 11.2929L12.75 5.75C12.3358 5.33579 11.6642 5.33579 11.25 5.75C10.8358 6.16421 10.8358 6.83579 11.25 7.25L15.4343 11.4343C15.7467 11.7467 15.7467 12.2533 15.4343 12.5657Z" fill="currentColor"></path>
                    </svg>
                </span>
                <!--end::Svg Icon-->
                               </a>';
                })
                ->rawColumns(['approved','actions'])
                ->toJson();
        }
        return view('customer.Sacrifice.index');
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        return view('customer.Sacrifice.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {

        // Get the current date in the format 'daymonthyear'
        $date = now()->format('dmY');

        // Generate a unique code (e.g., random string or incrementing number)
        $code = Str::random(4); // You can change this to generate your code

        // Combine the date and code to form the reference
        $reference = "S-{$date}{$code}";

        $sacrifice = new Sacrifice();

        // Définir les attributs du sacrifice un par un
        $sacrifice->reference = $reference;
        $sacrifice->controlleur_id = $request->controlleur_id;
        $sacrifice->societe_nettoyage_id = $request->societe_nettoyage_id;
        $sacrifice->organisme_control_id = $request->organisme_control_id;
        $sacrifice->abattoire_id = $request->abattoire_id;
        $sacrifice->compte_id = $request->sacrificateur_id;
        $sacrifice->date_sacrifice = Carbon::createFromFormat('Y-m-d', $request->date_sacrifice);
        $sacrifice->user_id = Auth::user()->id;
        $sacrifice->reference_bete = $request->reference_bete;
        $sacrifice->categorie = $request->categorie;
        $sacrifice->classement = $request->classement;
        $sacrifice->race_bete = $request->race_bete;
        $sacrifice->age = $request->age;
        $sacrifice->poids = $request->poids;
        $sacrifice->date_arrive = Carbon::createFromFormat('Y-m-d', $request->date_arrive);
        $sacrifice->num_lot_sacrifice = $request->num_lot_sacrifice;
        $sacrifice->num_lot_elevage = $request->num_lot_elevage;

        $sacrifice->save();
        return redirect()->route('sacrifice.show',$sacrifice->id);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $record = Sacrifice::find($id);
        return view('customer.Sacrifice.show',compact('record'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $record = Sacrifice::find($id);
        return view('customer.Sacrifice.edit',compact('record'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        // Valider la requête
        $request->validate([
            'image' => 'required|image|mimes:jpeg,png,jpg|max:2048', // Ajuster les règles de validation selon vos besoins
        ]);

        // Récupérer le produit
        $record = Sacrifice::findOrFail($id);

        // Enregistrer la nouvelle image
        if ($request->hasFile('image')) {
            $image = $request->file('image');
            $imageName = time() . '.' . $image->getClientOriginalExtension();
            $image->move(public_path('upload/sacrifice'), $imageName);

            // Mettre à jour le chemin de l'image du produit
            $record->image = 'upload/sacrifice/' . $imageName;
            $record->save();
        }

        return redirect()->back();
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}
