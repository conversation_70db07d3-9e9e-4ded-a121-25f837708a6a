<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Fabrication extends Model
{
    use HasFactory;

    protected $fillable = [
        'reference',
        'controlleur_id',
        'compte_id',
        'date_fabrication',
        'user_id',
        'num_lot_decoupe',
        'num_lot_elevage',
        'num_lot_producteur',
        'num_lot_fabrication',
        'produit',
        'poids',
        'quantite',
        'dlc'
    ];

    public function controlleur(){
        return $this->belongsTo(Controlleur::class,'controlleur_id','id');
    }

    public function compte(){
        return $this->belongsTo(Compte::class,'compte_id','id');
    }

    public function organisme_control(){
        return $this->belongsTo(Organismcontrolle::class,'organisme_control_id','id');
    }

}
