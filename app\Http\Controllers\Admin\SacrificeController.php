<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Sacrifice;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class SacrificeController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            $data = Sacrifice::all();

            return datatables()->of($data)
                ->addColumn('controlleur', function (Sacrifice $sacrifice) {
                    // Check if 'controlleur' relationship exists before accessing 'nom'
                    return $sacrifice->controlleur ? $sacrifice->controlleur->nom : '';
                })
                ->addColumn('compte', function (Sacrifice $sacrifice) {
                    // Check if 'producteur' relationship exists before accessing 'nom'
                    return $sacrifice->compte ? $sacrifice->compte->name : '';
                })
                ->addColumn('compte', function (Sacrifice $sacrifice) {
                    // Check if 'producteur' relationship exists before accessing 'nom'
                    return $sacrifice->compte ? $sacrifice->compte->name : '';
                })
                ->addColumn('approved', function (Sacrifice $sacrifice) {
                    // Set badge text based on the value of 'approved'
                    $badge = $sacrifice->approved == 1 ? '<span class="badge badge-light-success">Approuvé</span>' : '<span class="badge badge-light-danger">Non Approuvé</span>';
                    return $badge;
                })
                ->addColumn('actions', function (Sacrifice $sacrifice) {
                    $showButton = '<a href="'.route('sacrifices.show', $sacrifice->id).'" class="btn btn-sm btn-icon btn-bg-light btn-active-color-primary">
                                        <span class="svg-icon svg-icon-2">
                                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-eyeglasses" viewBox="0 0 16 16">
                                                <path d="M4 6a2 2 0 1 1 0 4 2 2 0 0 1 0-4m2.625.547a3 3 0 0 0-5.584.953H.5a.5.5 0 0 0 0 1h.541A3 3 0 0 0 7 8a1 1 0 0 1 2 0 3 3 0 0 0 5.959.5h.541a.5.5 0 0 0 0-1h-.541a3 3 0 0 0-5.584-.953A2 2 0 0 0 8 6c-.532 0-1.016.208-1.375.547M14 8a2 2 0 1 1-4 0 2 2 0 0 1 4 0"/>
                                            </svg>
                                        </span>
                                    </a>';

                    $duplicateButton = '<a href="'.route('sacrifices.duplicate', $sacrifice->id).'" class="btn btn-sm btn-icon btn-bg-light btn-active-color-primary">
                                            <span class="svg-icon svg-icon-2">
                                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-copy" viewBox="0 0 16 16">
                                                    <path fill-rule="evenodd" d="M4 2a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2zm2-1a1 1 0 0 0-1 1v8a1 1 0 0 0 1 1h8a1 1 0 0 0 1-1V2a1 1 0 0 0-1-1zM2 5a1 1 0 0 0-1 1v8a1 1 0 0 0 1 1h8a1 1 0 0 0 1-1v-1h1v1a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h1v1z"/>
                                                </svg>
                                            </span>
                                        </a>';

                    return $showButton . ' ' . $duplicateButton;
                })

                ->rawColumns(['approved','actions'])
                ->toJson();
        }
        return view('admin.Sacrifice.index');
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        return view('admin.Sacrifice.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {

        // Get the current date in the format 'daymonthyear'
        $date = now()->format('dmY');

        // Generate a unique code (e.g., random string or incrementing number)
        $code = Str::random(4); // You can change this to generate your code

        // Combine the date and code to form the reference
        $reference = "S-{$date}{$code}";

        $sacrifice = new Sacrifice();

        // Définir les attributs du sacrifice un par un
        $sacrifice->reference = $reference;
        $sacrifice->controlleur_id = $request->controlleur_id;
        $sacrifice->societe_nettoyage_id = $request->societe_nettoyage_id;
        $sacrifice->organisme_control_id = $request->organisme_control_id;
        //$sacrifice->abattoire_id = $request->abattoire_id;
        $sacrifice->compte_id = $request->compte_id;
        $sacrifice->date_sacrifice = Carbon::createFromFormat('Y-m-d', $request->date_sacrifice);
        $sacrifice->user_id = Auth::user()->id;
        $sacrifice->reference_bete = $request->reference_bete;
        $sacrifice->categorie = $request->categorie;
        $sacrifice->classement = $request->classement;
        $sacrifice->race_bete = $request->race_bete;
        $sacrifice->age = $request->age;
        $sacrifice->poids = $request->poids;
        $sacrifice->date_arrive = Carbon::createFromFormat('Y-m-d', $request->date_arrive);
        $sacrifice->num_lot_sacrifice = $request->num_lot_sacrifice;
        $sacrifice->num_lot_elevage = $request->num_lot_elevage;

        $sacrifice->save();
        return redirect()->route('sacrifices.show',$sacrifice->id);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $record = Sacrifice::find($id);
        return view('admin.Sacrifice.show',compact('record'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $record = Sacrifice::find($id);
        return view('admin.Sacrifice.edit',compact('record'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {


        // Récupérer le produit
        $record = Sacrifice::findOrFail($id);
        $record->controlleur_id = $request->controlleur_id;
        $record->societe_nettoyage_id = $request->societe_nettoyage_id;
        $record->organisme_control_id = $request->organisme_control_id;
        //$record->abattoire_id = $request->abattoire_id;
        $record->compte_id = $request->compte_id;
        $record->date_sacrifice = Carbon::createFromFormat('Y-m-d', $request->date_sacrifice);
        $record->reference_bete = $request->reference_bete;
        $record->categorie = $request->categorie;
        $record->classement = $request->classement;
        $record->race_bete = $request->race_bete;
        $record->age = $request->age;
        $record->poids = $request->poids;
        $record->date_arrive = Carbon::createFromFormat('Y-m-d', $request->date_arrive);
        $record->num_lot_sacrifice = $request->num_lot_sacrifice;
        $record->num_lot_elevage = $request->num_lot_elevage;

        // Enregistrer la nouvelle image
        if ($request->hasFile('image')) {
            $image = $request->file('image');
            $imageName = time() . '.' . $image->getClientOriginalExtension();
            $image->move(public_path('upload/sacrifice'), $imageName);

            // Mettre à jour le chemin de l'image du produit
            $record->image = 'upload/sacrifice/' . $imageName;

        }
        $record->save();
        return redirect()->route('sacrifices.show',$record->id);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        try {
            // Trouver l'objet à supprimer dans la base de données
            $controlleur = Sacrifice::findOrFail($id);

            // Supprimer l'objet
            $controlleur->delete();

            return redirect()->route('sacrifices.index');

        } catch (\Exception $e) {
            // Retourner une réponse d'erreur si une exception est levée
            return response()->json(['error' => 'Erreur lors de la suppression du contrôleur : ' . $e->getMessage()], 500);
        }
    }

    public function toggleApproved($id)
    {
        $production = Sacrifice::findOrFail($id);
        $production->approved = !$production->approved;
        $production->save();

        return response()->json(['success' => true, 'approved' => $production->approved]);
    }


    public function showCertificat($id)
    {
        $record = Sacrifice::find($id);
        return view('admin.Sacrifice.print',compact('record'));
    }


    public function duplicate($id)
    {
        // Find the original sacrifice record
        $originalSacrifice = Sacrifice::find($id);

        if (!$originalSacrifice) {
            return redirect()->route('sacrifices.index')->with('error', 'Sacrifice not found.');
        }

        // Duplicate the sacrifice record
        $newSacrifice = $originalSacrifice->replicate();

        // Generate new reference
        $date = now()->format('dmY');
        $code = Str::random(4); // You can change this to generate your code
        $reference = "S-{$date}{$code}";

        // Assign the new reference to the duplicated record
        $newSacrifice->reference = $reference;
        $newSacrifice->approved = 0;

        // Save the duplicated record
        $newSacrifice->save();

        return redirect()->route('sacrifices.index')->with('success', 'Sacrifice duplicated successfully.');
    }

}
