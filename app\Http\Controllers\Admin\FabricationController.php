<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Fabrication;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class FabricationController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            $data = Fabrication::all();
            return datatables()->of($data)

                ->addColumn('controlleur_id', function (Fabrication $fabrication) {
                    // Check if 'controlleur' relationship exists before accessing 'nom'
                    return $fabrication->controlleur ? $fabrication->controlleur->nom : '';
                })
                ->addColumn('compte_id', function (Fabrication $fabrication) {
                    // Check if 'controlleur' relationship exists before accessing 'nom'
                    return $fabrication->compte_id ? $fabrication->compte->name : '';
                })
                ->addColumn('approved', function (Fabrication $fabrication) {
                    // Set badge text based on the value of 'approved'
                    $badge = $fabrication->approved == 1 ? '<span class="badge badge-light-success">Approuvé</span>' : '<span class="badge badge-light-danger">Non Approuvé</span>';
                    return $badge;
                })
                ->addColumn('actions', function (Fabrication $fabrication) {
                    $showButton = '<a href="'.route('fabrications.show', $fabrication->id).'" class="btn btn-sm btn-icon btn-bg-light btn-active-color-primary">
        <span class="svg-icon svg-icon-2">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-eyeglasses" viewBox="0 0 16 16">
                <path d="M4 6a2 2 0 1 1 0 4 2 2 0 0 1 0-4m2.625.547a3 3 0 0 0-5.584.953H.5a.5.5 0 0 0 0 1h.541A3 3 0 0 0 7 8a1 1 0 0 1 2 0 3 3 0 0 0 5.959.5h.541a.5.5 0 0 0 0-1h-.541a3 3 0 0 0-5.584-.953A2 2 0 0 0 8 6c-.532 0-1.016.208-1.375.547M14 8a2 2 0 1 1-4 0 2 2 0 0 1 4 0"/>
            </svg>
        </span>
    </a>';

                    $duplicateButton = '<a href="'.route('fabrications.duplicate', $fabrication->id).'" class="btn btn-sm btn-icon btn-bg-light btn-active-color-primary">
        <span class="svg-icon svg-icon-2">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-copy" viewBox="0 0 16 16">
                <path fill-rule="evenodd" d="M4 2a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2zm2-1a1 1 0 0 0-1 1v8a1 1 0 0 0 1 1h8a1 1 0 0 0 1-1V2a1 1 0 0 0-1-1zM2 5a1 1 0 0 0-1 1v8a1 1 0 0 0 1 1h8a1 1 0 0 0 1-1v-1h1v1a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h1v1z"/>
            </svg>
        </span>
    </a>';

                    return $showButton . ' ' . $duplicateButton;
                })
                ->rawColumns(['approved','actions','compte'])
                ->toJson();
        }
        return view('admin.Fabrication.index');
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        return view('admin.Fabrication.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $date = now()->format('dmY');
        $code = Str::random(4);
        $reference = "F-{$date}{$code}";

        while (Fabrication::where('reference', $reference)->exists()) {
            $code = Str::random(4);
            $reference = "F-{$date}{$code}";
        }

        $record = new Fabrication();
        $record->reference = $reference;
        $record->controlleur_id = $request->controlleur_id;
        $record->organisme_control_id = $request->organisme_control_id;
        $record->compte_id = $request->compte_id;
        $record->date_fabrication = Carbon::createFromFormat('Y-m-d', $request->date_fabrication);
        $record->user_id = Auth::user()->id;
        $record->num_lot_decoupe = $request->num_lot_decoupe;
        $record->num_lot_elevage = $request->num_lot_elevage;
        $record->num_lot_production = $request->num_lot_production;
        $record->num_lot_fabrication = $request->num_lot_fabrication;
        $record->produit = $request->produit;
        $record->poids = $request->poids;
        $record->quantite = $request->quantite;
        $record->dlc = Carbon::createFromFormat('Y-m-d', $request->dlc);
        $record->save();

        return redirect()->route('fabrications.show',$record->id);

    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $record = Fabrication::find($id);
        return view('admin.Fabrication.show',compact('record'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $record = Fabrication::find($id);
        return view('admin.Fabrication.edit',compact('record'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {

        $record = Fabrication::findOrFail($id);
        //$record->reference = $reference;
        $record->controlleur_id = $request->controlleur_id;
        $record->compte_id = $request->compte_id;
        $record->organisme_control_id = $request->organisme_control_id;
        $record->date_fabrication = Carbon::createFromFormat('Y-m-d', $request->date_fabrication);
        //$record->user_id = Auth::user()->id;
        $record->num_lot_decoupe = $request->num_lot_decoupe;
        $record->num_lot_elevage = $request->num_lot_elevage;
        $record->num_lot_production = $request->num_lot_production;
        $record->num_lot_fabrication = $request->num_lot_fabrication;
        $record->produit = $request->produit;
        $record->poids = $request->poids;
        $record->quantite = $request->quantite;
        $record->dlc = Carbon::createFromFormat('Y-m-d', $request->dlc);

        // Enregistrer la nouvelle image
        if ($request->hasFile('image')) {
            $image = $request->file('image');
            $imageName = time() . '.' . $image->getClientOriginalExtension();
            $image->move(public_path('upload/fabrication'), $imageName);

            // Mettre à jour le chemin de l'image du produit
            $record->image = 'upload/fabrication/' . $imageName;

        }
        $record->save();

        return redirect()->route('fabrications.show',$record->id);

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $record = Fabrication::findOrFail($id);
        $record->delete();
        return redirect()->route('fabrications.index');
    }


    public function toggleApproved($id)
    {
        $production = Fabrication::findOrFail($id);
        $production->approved = !$production->approved;
        $production->save();
        return response()->json(['success' => true, 'approved' => $production->approved]);
    }

    public function showCertificat($id)
    {
        $record = Fabrication::find($id);
        return view('admin.Fabrication.print',compact('record'));
    }


    public function duplicate($id)
    {
        // Find the original fabrication record
        $originalFabrication = Fabrication::find($id);

        if (!$originalFabrication) {
            return redirect()->route('fabrications.index')->with('error', 'Fabrication not found.');
        }

        // Duplicate the fabrication record
        $newFabrication = $originalFabrication->replicate();

        // Generate new reference
        $date = now()->format('dmY');
        $code = Str::random(4); // You can change this to generate your code
        $reference = "F-{$date}{$code}";

        // Assign the new reference to the duplicated record
        $newFabrication->reference = $reference;
        $newFabrication->approved = 0;

        // Save the duplicated record
        $newFabrication->save();

        return redirect()->route('fabrications.index')->with('success', 'Fabrication duplicated successfully.');
    }

}
