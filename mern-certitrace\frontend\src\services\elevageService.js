import { apiGet, apiPost, apiPut, apiDelete, apiDownload } from './api'

const elevageService = {
  getAll: async (params = {}) => {
    const queryString = new URLSearchParams(params).toString()
    return await apiGet(`/elevages?${queryString}`)
  },
  getById: async (id) => await apiGet(`/elevages/${id}`),
  create: async (data) => await apiPost('/elevages', data),
  update: async (id, data) => await apiPut(`/elevages/${id}`, data),
  delete: async (id) => await apiDelete(`/elevages/${id}`),
  approve: async (id) => await apiPost(`/elevages/${id}/approve`),
  generateCertificate: async (id) => await apiGet(`/elevages/${id}/certificate`),
  downloadCertificate: async (id, filename = 'elevage-certificate.pdf') => {
    await apiDownload(`/elevages/${id}/certificate/pdf`, filename)
  },
}

export default elevageService
