import express from 'express';
import { protect, adminOrCustomer } from '../middleware/authMiddleware.js';

const router = express.Router();

// Placeholder routes for Search functionality
router.get('/', protect, adminOrCustomer, (req, res) => {
  res.json({ message: 'Global search - to be implemented' });
});

router.get('/productions', protect, adminOrCustomer, (req, res) => {
  res.json({ message: 'Search productions - to be implemented' });
});

router.get('/elevages', protect, adminOrCustomer, (req, res) => {
  res.json({ message: 'Search elevages - to be implemented' });
});

router.get('/sacrifices', protect, adminOrCustomer, (req, res) => {
  res.json({ message: 'Search sacrifices - to be implemented' });
});

router.get('/decoupes', protect, adminOrCustomer, (req, res) => {
  res.json({ message: 'Search decoupes - to be implemented' });
});

router.get('/fabrications', protect, adminOrCustomer, (req, res) => {
  res.json({ message: 'Search fabrications - to be implemented' });
});

router.get('/conditionnements', protect, adminOrCustomer, (req, res) => {
  res.json({ message: 'Search conditionnements - to be implemented' });
});

router.get('/logistiques', protect, adminOrCustomer, (req, res) => {
  res.json({ message: 'Search logistiques - to be implemented' });
});

export default router;
