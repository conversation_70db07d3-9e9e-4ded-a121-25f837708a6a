<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Agrementc;
use App\Models\Compte;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;
use Illuminate\Support\Facades\Mail;


class AgrementcController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        // 1. Handle AJAX calls only when it *is* an AJAX request
        if ($request->ajax()) {

            /**
             * 2.  Let DataTables work with an Eloquent *builder* rather than a
             *    fully‑hydrated collection.  It’s faster and allows server‑side
             *    searching / ordering automatically.
             */
            $query = Agrementc::select(['id', 'compte_id', 'numero', 'created_at']);

            return DataTables::of($query)      // ← plural “DataTables”, not “DataTable”

            ->addColumn('compte', function (Agrementc $Agrementc) {
                // Check if 'controlleur' relationship exists before accessing 'nom'
                return $Agrementc->compte ? $Agrementc->compte->name : '';
            })
            ->addColumn('actions', function (Agrementc $Agrementc) {
                // 3.  You can keep the inline HTML, or (cleaner) move it to a Blade partial
                return '
                    <a href="'.route('agrement_commerce.show', $Agrementc->id).'" class="btn btn-sm btn-icon btn-bg-light btn-active-color-primary">
                <!--begin::Svg Icon | path: icons/duotune/arrows/arr064.svg-->
                <span class="svg-icon svg-icon-2">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <rect opacity="0.5" x="18" y="13" width="13" height="2" rx="1" transform="rotate(-180 18 13)" fill="currentColor"></rect>
                        <path d="M15.4343 12.5657L11.25 16.75C10.8358 17.1642 10.8358 17.8358 11.25 18.25C11.6642 18.6642 12.3358 18.6642 12.75 18.25L18.2929 12.7071C18.6834 12.3166 18.6834 11.6834 18.2929 11.2929L12.75 5.75C12.3358 5.33579 11.6642 5.33579 11.25 5.75C10.8358 6.16421 10.8358 6.83579 11.25 7.25L15.4343 11.4343C15.7467 11.7467 15.7467 12.2533 15.4343 12.5657Z" fill="currentColor"></path>
                    </svg>
                </span>
                <!--end::Svg Icon-->
                               </a>';
            })
                ->rawColumns(['actions'])      // 4.  Let DataTables know “actions” is raw HTML
                ->make(true);                  // 5.  Use ->make(true) (or ->toJson()) either is fine
        }

        // 6.  Non‑AJAX: just render the page
        return view('admin.Agrementc.index');
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        return view('admin.Agrementc.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $record = new Agrementc();
        $record->compte_id = $request ->compte_id;
        $record->numero = $request ->numero;
        $record->save();

        return redirect()->route('agrement_commerce.show',$record->id);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $record = Agrementc::find($id);
        return view('admin.Agrementc.show',compact('record'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        // ✅ Récupérer l'agrément à modifier
        $agrement = \App\Models\Agrementc::findOrFail($id);

        // ✅ Retourner la vue avec les données de l'agrément
        return view('admin.Agrementc.edit', compact('agrement'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        // ✅ Récupération du modèle existant
        $record = \App\Models\Agrementc::findOrFail($id);

        // ✅ Validation (optionnelle mais recommandée)
        $request->validate([
            'compte_id' => 'required|exists:comptes,id',
            'numero' => 'required|string|max:255',
        ]);

        // ✅ Mise à jour des champs
        $record->compte_id = $request->compte_id;
        $record->numero = $request->numero;
        $record->save();

        // ✅ Redirection avec succès
        return redirect()->route('agrement_commerce.show', $record->id)
            ->with('success', 'L’agrément a été mis à jour avec succès.');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        try {
            // ✅ Récupération de l'enregistrement
            $record = \App\Models\Agrementc::findOrFail($id);

            // ✅ Suppression
            $record->delete();

            // ✅ Redirection avec message de succès
            return redirect()->route('agrement_commerce.index')
                ->with('success', 'L’agrément a été supprimé avec succès.');
        } catch (\Exception $e) {
            // ⚠️ Gestion d'erreur
            return redirect()->back()->withErrors([
                'error' => 'Une erreur est survenue lors de la suppression : ' . $e->getMessage()
            ]);
        }
    }



    public function print($id)
    {
        $agrementc = Agrementc::findOrFail($id);
        return view('admin.Agrementc.print', compact('agrementc'));

    }
}
