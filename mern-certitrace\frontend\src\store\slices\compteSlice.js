import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'

const initialState = {
  comptes: [],
  currentCompte: null,
  isLoading: false,
  error: null,
  pagination: { page: 1, limit: 10, total: 0, totalPages: 0 },
  filters: { search: '', status: '', category: '' },
}

// Placeholder thunks
export const fetchComptes = createAsyncThunk('compte/fetchComptes', async () => ({ data: [], pagination: initialState.pagination }))
export const fetchCompteById = createAsyncThunk('compte/fetchCompteById', async () => ({ data: null }))
export const createCompte = createAsyncThunk('compte/createCompte', async (data) => ({ data }))
export const updateCompte = createAsyncThunk('compte/updateCompte', async ({ id, data }) => ({ data: { ...data, _id: id } }))
export const deleteCompte = createAsyncThunk('compte/deleteCompte', async (id) => id)

const compteSlice = createSlice({
  name: 'compte',
  initialState,
  reducers: {
    clearError: (state) => { state.error = null },
    clearCurrentCompte: (state) => { state.currentCompte = null },
    setFilters: (state, action) => { state.filters = { ...state.filters, ...action.payload } },
    clearFilters: (state) => { state.filters = { search: '', status: '', category: '' } },
    setPagination: (state, action) => { state.pagination = { ...state.pagination, ...action.payload } },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchComptes.pending, (state) => { state.isLoading = true })
      .addCase(fetchComptes.fulfilled, (state, action) => { state.isLoading = false; state.comptes = action.payload.data })
      .addCase(fetchCompteById.fulfilled, (state, action) => { state.currentCompte = action.payload.data })
      .addCase(createCompte.fulfilled, (state, action) => { state.comptes.unshift(action.payload.data) })
      .addCase(updateCompte.fulfilled, (state, action) => {
        const index = state.comptes.findIndex(c => c._id === action.payload.data._id)
        if (index !== -1) state.comptes[index] = action.payload.data
        state.currentCompte = action.payload.data
      })
      .addCase(deleteCompte.fulfilled, (state, action) => {
        state.comptes = state.comptes.filter(c => c._id !== action.payload)
      })
  },
})

export const { clearError, clearCurrentCompte, setFilters, clearFilters, setPagination } = compteSlice.actions
export default compteSlice.reducer
