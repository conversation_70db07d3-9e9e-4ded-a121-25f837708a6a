import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'

const initialState = {
  conditionnements: [],
  currentConditionnement: null,
  isLoading: false,
  error: null,
  pagination: { page: 1, limit: 10, total: 0, totalPages: 0 },
  filters: { search: '', status: '', dateRange: null, compte: '' },
}

// Placeholder thunks
export const fetchConditionnements = createAsyncThunk('conditionnement/fetchConditionnements', async () => ({ data: [], pagination: initialState.pagination }))
export const fetchConditionnementById = createAsyncThunk('conditionnement/fetchConditionnementById', async () => ({ data: null }))
export const createConditionnement = createAsyncThunk('conditionnement/createConditionnement', async (data) => ({ data }))
export const updateConditionnement = createAsyncThunk('conditionnement/updateConditionnement', async ({ id, data }) => ({ data: { ...data, _id: id } }))
export const deleteConditionnement = createAsyncThunk('conditionnement/deleteConditionnement', async (id) => id)

const conditionnementSlice = createSlice({
  name: 'conditionnement',
  initialState,
  reducers: {
    clearError: (state) => { state.error = null },
    clearCurrentConditionnement: (state) => { state.currentConditionnement = null },
    setFilters: (state, action) => { state.filters = { ...state.filters, ...action.payload } },
    clearFilters: (state) => { state.filters = { search: '', status: '', dateRange: null, compte: '' } },
    setPagination: (state, action) => { state.pagination = { ...state.pagination, ...action.payload } },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchConditionnements.pending, (state) => { state.isLoading = true })
      .addCase(fetchConditionnements.fulfilled, (state, action) => { state.isLoading = false; state.conditionnements = action.payload.data })
      .addCase(fetchConditionnementById.fulfilled, (state, action) => { state.currentConditionnement = action.payload.data })
      .addCase(createConditionnement.fulfilled, (state, action) => { state.conditionnements.unshift(action.payload.data) })
      .addCase(updateConditionnement.fulfilled, (state, action) => {
        const index = state.conditionnements.findIndex(c => c._id === action.payload.data._id)
        if (index !== -1) state.conditionnements[index] = action.payload.data
        state.currentConditionnement = action.payload.data
      })
      .addCase(deleteConditionnement.fulfilled, (state, action) => {
        state.conditionnements = state.conditionnements.filter(c => c._id !== action.payload)
      })
  },
})

export const { clearError, clearCurrentConditionnement, setFilters, clearFilters, setPagination } = conditionnementSlice.actions
export default conditionnementSlice.reducer
