<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Production;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\Session;

class ProductionImportController extends Controller
{
    public function showForm()
    {
        return view('admin.Production.import.upload');
    }

    public function upload(Request $request)
    {
        $request->validate([
            'file' => 'required|file|mimes:xlsx,csv',
        ]);

        $path = $request->file('file')->store('temp');
        $data = Excel::toArray([], storage_path("app/$path"));

        Session::put('import_production_path', $path);
        Session::put('import_production_data', $data[0]);

        return view('admin.Production.import.preview', ['rows' => $data[0]]);
    }

    public function importConfirm(Request $request)
    {
        $rows = Session::get('import_production_data');

        foreach ($rows as $index => $row) {
            if ($index === 0) continue; // skip headers

            Production::create([
                'reference' => $row[0],
                'controlleur_id' => $row[1],
                'compte_id' => $row[2],
                'organisme_control_id' => $row[3],
                'date_debut' => $row[4],
                'date_fin' => $row[5],
                'user_id' => $row[6],
                'approved' => $row[7] ?? 0,
                'image' => $row[8],
                'produit' => $row[9],
                'poids' => $row[10],
                'quantite' => $row[11],
                'origine' => $row[12],
                'num_lot_production' => $row[13],
                'dlc' => $row[14],
            ]);
        }

        Session::forget(['import_production_data', 'import_production_path']);
        return redirect()->route('productions.index')->with('success', 'Importation réussie !');
    }

}
