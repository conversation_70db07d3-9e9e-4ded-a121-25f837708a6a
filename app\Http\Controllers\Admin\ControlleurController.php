<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Controlleur;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Yajra\DataTables\DataTables;

class ControlleurController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            $controlleurs = Controlleur::all();
            return DataTables::of($controlleurs)
                ->addColumn('actions', function (Controlleur $Controlleur) {
                    // Generate action button HTML with route
                    return '<a href="'.route('controlleurs.show', $Controlleur->id).'" class="btn btn-sm btn-icon btn-bg-light btn-active-color-primary">
                                <!--begin::Svg Icon | path: icons/duotune/arrows/arr064.svg-->
                                <span class="svg-icon svg-icon-2">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                        <rect opacity="0.5" x="18" y="13" width="13" height="2" rx="1" transform="rotate(-180 18 13)" fill="currentColor"></rect>
                                        <path d="M15.4343 12.5657L11.25 16.75C10.8358 17.1642 10.8358 17.8358 11.25 18.25C11.6642 18.6642 12.3358 18.6642 12.75 18.25L18.2929 12.7071C18.6834 12.3166 18.6834 11.6834 18.2929 11.2929L12.75 5.75C12.3358 5.33579 11.6642 5.33579 11.25 5.75C10.8358 6.16421 10.8358 6.83579 11.25 7.25L15.4343 11.4343C15.7467 11.7467 15.7467 12.2533 15.4343 12.5657Z" fill="currentColor"></path>
                                    </svg>
                                </span>
                                <!--end::Svg Icon-->
                             </a>';
                })
                ->rawColumns(['actions'])
                ->toJson();
        }
        return view('admin.controlleur.index');
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        return view('admin.controlleur.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        // Get the current date in the format 'daymonthyear'
        $date = now()->format('dmY');

        // Generate a unique code (e.g., random string or incrementing number)
        $code = Str::random(4); // You can change this to generate your code

        // Combine the date and code to form the reference
        $reference = "CONTRO-{$date}{$code}";

        $controlleur = new Controlleur();
        $controlleur->reference = $reference;
        $controlleur->nom = $request->nom;
        $controlleur->email = $request->email;
        $controlleur->telephone = $request->telephone;
        $controlleur->adresse = $request->adresse;
        $controlleur->organismcontrolle_id = $request->organismcontrolle_id;
        if ($request->hasFile('image')) {
            $image = $request->file('image');
            $imageName = time() . '.' . $image->getClientOriginalExtension();
            $image->move(public_path('upload/controlleur'), $imageName);

            // Mettre à jour le chemin de l'image du produit
            $controlleur->image = 'upload/controlleur/' . $imageName;

        }
        $controlleur->save();
        return redirect()->route('controlleurs.show', $controlleur->id);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $record = Controlleur::find($id);
        return view('admin.controlleur.show',compact('record'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $record = Controlleur::find($id);
        return view('admin.controlleur.edit',compact('record'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $controlleur = Controlleur::findOrFail($id);

        // Update the Controlleur instance with new data
        $controlleur->nom = $request->nom;
        $controlleur->email = $request->email;
        $controlleur->telephone = $request->telephone;
        $controlleur->adresse = $request->adresse;
        $controlleur->organismcontrolle_id = $request->organismcontrolle_id;

        if ($request->hasFile('image')) {
            // Delete the old image if it exists
            if ($controlleur->image) {
                Storage::delete('public/' . $controlleur->image);
            }

            // Handle new file upload
            $image = $request->file('image');
            $imageName = time() . '.' . $image->getClientOriginalExtension();
            $image->move(public_path('upload/controlleur'), $imageName);

            // Update the image path
            $controlleur->image = 'upload/controlleur/' . $imageName;
        }

        // Save the updated Controlleur instance to the database
        $controlleur->save();
        return redirect()->route('controlleurs.show', $controlleur->id);

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        try {
            // Trouver l'objet à supprimer dans la base de données
            $controlleur = Controlleur::findOrFail($id);

            // Supprimer l'objet
            $controlleur->delete();

            return view('admin.controlleur.index');

        } catch (\Exception $e) {
            // Retourner une réponse d'erreur si une exception est levée
            return response()->json(['error' => 'Erreur lors de la suppression du contrôleur : ' . $e->getMessage()], 500);
        }
    }
}
