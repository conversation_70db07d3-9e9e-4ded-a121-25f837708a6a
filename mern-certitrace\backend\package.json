{"name": "certitrace-backend", "version": "1.0.0", "description": "Backend API for CertiTrace - Food Production Certification System", "main": "src/server.js", "type": "module", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "build": "echo 'No build step required for Node.js'", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/", "lint:fix": "eslint src/ --fix"}, "keywords": ["certification", "traceability", "food-production", "mern", "api"], "author": "CertiTrace Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "mongoose": "^8.0.3", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "express-validator": "^7.0.1", "multer": "^1.4.5-lts.1", "exceljs": "^4.4.0", "pdfkit": "^0.14.0", "nodemailer": "^6.9.7", "compression": "^1.7.4", "express-rate-limit": "^7.1.5", "socket.io": "^4.7.4", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "joi": "^17.11.0", "moment": "^2.29.4", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.55.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.0.1", "prettier": "^3.1.0", "@types/jest": "^29.5.8"}, "engines": {"node": ">=18.0.0"}}