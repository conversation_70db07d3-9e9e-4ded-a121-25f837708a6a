<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Elevage extends Model
{
    use HasFactory;

    protected $fillable = [
        'reference',
        'compte_id',
        'controlleur_id',
        'organisme_control_id',
        'date_debut',
        'date_fin',
        'user_id',
        'approved',
        'produit',
        'origine',
        'poids',
        'quantite',
        'age',
        'num_lot_elevage',
    ];


    public function compte(){
        return $this->belongsTo(Compte::class,'compte_id','id');
    }

    public function controlleur(){
        return $this->belongsTo(Controlleur::class,'controlleur_id','id');
    }

    public function user(){
        return $this->belongsTo(User::class,'user_id','id');
    }

    public function organisme_control(){
        return $this->belongsTo(Organismcontrolle::class,'organisme_control_id','id');
    }
}
