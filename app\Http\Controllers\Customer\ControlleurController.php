<?php

namespace App\Http\Controllers\Customer;

use App\Http\Controllers\Controller;
use App\Models\Controlleur;
use Illuminate\Http\Request;

class ControlleurController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            $data = Controlleur::all();
            return datatables()->of($data)
                ->addColumn('image', function (Controlleur $controlleur) {
                    // Vérifiez si le champ 'image' est défini
                    if ($controlleur->image) {
                        // Si oui, retournez le code HTML avec l'image
                        return '<div class="symbol symbol-45px me-5"><img src="assets/media/avatars/300-5.jpg"' . $controlleur->image . '" alt=""></div>';
                    } else {
                        // Sinon, retournez une chaîne vide
                        return '';
                    }
                })
                ->rawColumns(['image'])
                ->toJson();
        }
        return view('customer.Controlleur.index');
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}
