import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'

const initialState = {
  logistiques: [],
  currentLogistique: null,
  isLoading: false,
  error: null,
  pagination: { page: 1, limit: 10, total: 0, totalPages: 0 },
  filters: { search: '', status: '', dateRange: null, compte: '' },
}

// Placeholder thunks
export const fetchLogistiques = createAsyncThunk('logistique/fetchLogistiques', async () => ({ data: [], pagination: initialState.pagination }))
export const fetchLogistiqueById = createAsyncThunk('logistique/fetchLogistiqueById', async () => ({ data: null }))
export const createLogistique = createAsyncThunk('logistique/createLogistique', async (data) => ({ data }))
export const updateLogistique = createAsyncThunk('logistique/updateLogistique', async ({ id, data }) => ({ data: { ...data, _id: id } }))
export const deleteLogistique = createAsyncThunk('logistique/deleteLogistique', async (id) => id)

const logistiqueSlice = createSlice({
  name: 'logistique',
  initialState,
  reducers: {
    clearError: (state) => { state.error = null },
    clearCurrentLogistique: (state) => { state.currentLogistique = null },
    setFilters: (state, action) => { state.filters = { ...state.filters, ...action.payload } },
    clearFilters: (state) => { state.filters = { search: '', status: '', dateRange: null, compte: '' } },
    setPagination: (state, action) => { state.pagination = { ...state.pagination, ...action.payload } },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchLogistiques.pending, (state) => { state.isLoading = true })
      .addCase(fetchLogistiques.fulfilled, (state, action) => { state.isLoading = false; state.logistiques = action.payload.data })
      .addCase(fetchLogistiqueById.fulfilled, (state, action) => { state.currentLogistique = action.payload.data })
      .addCase(createLogistique.fulfilled, (state, action) => { state.logistiques.unshift(action.payload.data) })
      .addCase(updateLogistique.fulfilled, (state, action) => {
        const index = state.logistiques.findIndex(l => l._id === action.payload.data._id)
        if (index !== -1) state.logistiques[index] = action.payload.data
        state.currentLogistique = action.payload.data
      })
      .addCase(deleteLogistique.fulfilled, (state, action) => {
        state.logistiques = state.logistiques.filter(l => l._id !== action.payload)
      })
  },
})

export const { clearError, clearCurrentLogistique, setFilters, clearFilters, setPagination } = logistiqueSlice.actions
export default logistiqueSlice.reducer
