<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Controlleur;
use App\Models\Production;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Yajra\DataTables\DataTables;

class ProductionController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            $records = Production::all(); // Eager load the 'controlleur' relationship
            return DataTables::of($records)
                ->addColumn('controlleur', function (Production $production) {
                    // Check if 'controlleur' relationship exists before accessing 'nom'
                    return $production->controlleur ? $production->controlleur->nom : '';
                })
                ->addColumn('compte', function (Production $production) {
                    // Check if 'producteur' relationship exists before accessing 'nom'
                    return $production->compte ? $production->compte->name : '';
                })

                ->addColumn('approved', function (Production $production) {
                    // Set badge text based on the value of 'approved'
                    $badge = $production->approved == 1 ? '<span class="badge badge-light-success">Approuvé</span>' : '<span class="badge badge-light-danger">Non Approuvé</span>';
                    return $badge;
                })
                ->addColumn('action', function (Production $production) {
                    // Generate action buttons HTML with routes
                    $showButton = '<a href="'.route('productions.show', $production->id).'" class="btn btn-sm btn-icon btn-bg-light btn-active-color-primary">
                        <span class="svg-icon svg-icon-2">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-eyeglasses" viewBox="0 0 16 16">
                              <path d="M4 6a2 2 0 1 1 0 4 2 2 0 0 1 0-4m2.625.547a3 3 0 0 0-5.584.953H.5a.5.5 0 0 0 0 1h.541A3 3 0 0 0 7 8a1 1 0 0 1 2 0 3 3 0 0 0 5.959.5h.541a.5.5 0 0 0 0-1h-.541a3 3 0 0 0-5.584-.953A2 2 0 0 0 8 6c-.532 0-1.016.208-1.375.547M14 8a2 2 0 1 1-4 0 2 2 0 0 1 4 0"/>
                            </svg>
                        </span>
                    </a>';

                    $duplicateButton = '<a href="'.route('productions.duplicate', $production->id).'" class="btn btn-sm btn-icon btn-bg-light btn-active-color-primary">
                            <span class="svg-icon svg-icon-2">
                               <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-copy" viewBox="0 0 16 16">
                                  <path fill-rule="evenodd" d="M4 2a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2zm2-1a1 1 0 0 0-1 1v8a1 1 0 0 0 1 1h8a1 1 0 0 0 1-1V2a1 1 0 0 0-1-1zM2 5a1 1 0 0 0-1 1v8a1 1 0 0 0 1 1h8a1 1 0 0 0 1-1v-1h1v1a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h1v1z"/>
                               </svg>
                            </span>
                        </a>';

                    return $showButton . ' ' . $duplicateButton;
                })


                ->rawColumns(['controlleur', 'compte', 'date_debut', 'approved', 'action']) // Mark columns as HTML/RAW
                ->make(true);
        }
        return view('admin.Production.index');
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        return view('admin.Production.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */



    public function store(Request $request)
    {
        // Get the current date in the format 'daymonthyear'
        $date = now()->format('dmY');

        // Generate a unique code (e.g., random string or incrementing number)
        $code = Str::random(4); // You can change this to generate your code

        // Combine the date and code to form the reference
        $reference = "P-{$date}{$code}";
        //dd($reference);
        // Create a new Production instance with the generated reference
        $record = new Production();
        $record->reference = $reference;
        $record->controlleur_id = $request->controlleur_id;
        $record->compte_id = $request->compte_id;
        $record->organisme_control_id = $request->organisme_control_id;
        $record->date_debut = Carbon::createFromFormat('Y-m-d', $request->date_debut);
        $record->date_fin = Carbon::createFromFormat('Y-m-d', $request->date_fin);
        $record->user_id = Auth::user()->id;
        $record->produit = $request->produit;
        $record->poids = $request->poids;
        $record->quantite = $request->quantite;
        $record->origine = $request->origine;
        $record->num_lot_production = $request->num_lot_production;
        $record->dlc = Carbon::createFromFormat('Y-m-d', $request->dlc);
        $record->save();

        return redirect()->route('productions.show',$record->id);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $record = Production::find($id);
        return view('admin.Production.show',compact('record'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $record = Production::find($id);
        return view('admin.Production.edit',compact('record'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $record = Production::findOrFail($id);
        $record->controlleur_id = $request->controlleur_id;
        $record->compte_id = $request->compte_id;
        $record->organisme_control_id = $request->organisme_control_id;
        $record->date_debut = Carbon::createFromFormat('Y-m-d', $request->date_debut);
        $record->date_fin = Carbon::createFromFormat('Y-m-d', $request->date_fin);
        $record->user_id = Auth::user()->id;
        $record->produit = $request->produit;
        $record->poids = $request->poids;
        $record->quantite = $request->quantite;
        $record->origine = $request->origine;
        $record->num_lot_production = $request->num_lot_production;
        $record->dlc = Carbon::createFromFormat('Y-m-d', $request->dlc);
        if ($request->hasFile('image')) {
            $image = $request->file('image');
            $imageName = time() . '.' . $image->getClientOriginalExtension();
            $image->move(public_path('upload/production'), $imageName);

            // Mettre à jour le chemin de l'image du produit
            $record->image = 'upload/production/' . $imageName;
        }
        $record->save();

        return redirect()->route('productions.show',$record->id);

        // Enregistrer la nouvelle image


        //return redirect()->back();
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        try {
            // Trouver l'objet à supprimer dans la base de données
            $controlleur = Production::findOrFail($id);

            // Supprimer l'objet
            $controlleur->delete();
            return redirect()->route('productions.index');

        } catch (\Exception $e) {
            // Retourner une réponse d'erreur si une exception est levée
            return response()->json(['error' => 'Erreur lors de la suppression du contrôleur : ' . $e->getMessage()], 500);
        }
    }


    public function toggleApproved($id)
    {
        $production = Production::findOrFail($id);
        $production->approved = !$production->approved;
        $production->save();

        return response()->json(['success' => true, 'approved' => $production->approved]);
    }



    public function showCertificat($id)
    {
        $record = Production::find($id);
        return view('admin.Production.print',compact('record'));
    }



    public function duplicate($id)
    {
        // Find the original production record
        $originalProduction = Production::find($id);

        if (!$originalProduction) {
            return redirect()->route('productions.index')->with('error', 'Production not found.');
        }

        // Duplicate the production record
        $newProduction = $originalProduction->replicate();

        // Generate new reference
        $date = now()->format('dmY');
        $code = Str::random(4); // You can change this to generate your code
        $reference = "P-{$date}{$code}";

        // Assign the new reference to the duplicated record
        $newProduction->reference = $reference;
        $newProduction->approved = 0;

        // Save the duplicated record
        $newProduction->save();

        return redirect()->route('productions.index')->with('success', 'Production duplicated successfully.');
    }





}
