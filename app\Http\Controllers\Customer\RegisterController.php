<?php

namespace App\Http\Controllers\Customer;

use App\Http\Controllers\Controller;
use App\Models\Compte;
use http\Client\Curl\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;

class RegisterController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {


        // Créer une nouvelle instance de Compte
        $compte = new Compte();
        $compte->name = $request->name;
        $compte->categoriecompte_id = $request->categoriecompte_id;
        $compte->telephone = $request->telephone;
        $compte->fax = $request->fax;
        $compte->email = $request->email;
        $compte->site_web = $request->site_web;
        $compte->adresse = $request->adresse;
        $compte->code_postal = $request->code_postal;
        $compte->ville = $request->ville;
        $compte->region = $request->region;
        $compte->pays = $request->pays;
        $compte->tva = $request->tva;
        $compte->siret = $request->siret;
        $compte->agrement = $request->agrement;
        $compte->user_id = 1;
        $compte->save();



        $user = new \App\Models\User();
        $user->name = $request->user_name;
        $user->email = $request->email;
        $user->password = Hash::make($request->password);
        $user->compte_id = $compte->id;
        $user->save();


        // Log in the user
        Auth::login($user);

        return redirect()->route('customer.dashboard');
        //return response()->json(['user' => $user], 201);

    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}
