<?php

namespace App\Http\Controllers;

use App\Models\Decoupe;
use App\Models\Elevage;
use App\Models\Fabrication;
use App\Models\Production;
use App\Models\Sacrifice;
use Illuminate\Http\Request;

class SearchController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    public function search(Request $request)
    {
        // Perform search across multiple models
        //dd($request);

        // Search in Production
        $productions = Production::where('num_lot_production', 'like', '%' . $request->search . '%')
            ->get();

        // Search in Elevage
        $elevages = Elevage::where('num_lot_elevage', 'like', '%' . $request->search . '%')
            ->get();

        // Search in Sacrifice
        $sacrifices = Sacrifice::where('num_lot_sacrifice', 'like', '%' . $request->search . '%')
            ->get();

        // Search in Decoupe
        $decoupes = Decoupe::where('num_lot_decoupe', 'like', '%' . $request->search . '%')
            ->get();

        // Search in Fabrication
        $fabrications = Fabrication::where('num_lot_fabrication', 'like', '%' . $request->search . '%')
            ->get();

        // Combine all results into a single collection
        $results = collect([$productions, $elevages, $sacrifices, $decoupes, $fabrications])
            ->collapse();

        // Return the search results to the view
        return view('search_result', compact('results'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}
