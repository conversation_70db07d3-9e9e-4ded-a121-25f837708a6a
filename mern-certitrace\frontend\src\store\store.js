import { configureStore } from '@reduxjs/toolkit'
import authSlice from './slices/authSlice'
import uiSlice from './slices/uiSlice'
import productionSlice from './slices/productionSlice'
import elevageSlice from './slices/elevageSlice'
import sacrificeSlice from './slices/sacrificeSlice'
import decoupeSlice from './slices/decoupeSlice'
import fabricationSlice from './slices/fabricationSlice'
import conditionnementSlice from './slices/conditionnementSlice'
import logistiqueSlice from './slices/logistiqueSlice'
import compteSlice from './slices/compteSlice'
import searchSlice from './slices/searchSlice'

export const store = configureStore({
  reducer: {
    auth: authSlice,
    ui: uiSlice,
    production: productionSlice,
    elevage: elevageSlice,
    sacrifice: sacrificeSlice,
    decoupe: decoupeSlice,
    fabrication: fabricationSlice,
    conditionnement: conditionnementSlice,
    logistique: logistiqueSlice,
    compte: compteSlice,
    search: searchSlice,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
      },
    }),
  devTools: import.meta.env.DEV,
})

export type RootState = ReturnType<typeof store.getState>
export type AppDispatch = typeof store.dispatch
