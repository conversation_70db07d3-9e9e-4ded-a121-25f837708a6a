<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Production extends Model
{
    use HasFactory;

    protected $fillable = [
        'controlleur_id',
        'compte_id',
        'organisme_control_id',
        'control',
        'responsable',
        'date_debut',
        'date_fin',
        'user_id',
        'typecertificat_id',
        'certificat',
        'approved',
        'reference',
        'produit',
        'poids',
        'quantite',
        'origine',
        'num_lot_production',
        'dlc'
    ];
    public function controlleur(){
        return $this->belongsTo(Controlleur::class,'controlleur_id','id');
    }

    public function compte(){
        return $this->belongsTo(Compte::class,'compte_id','id');
    }

    public function organisme_control(){
        return $this->belongsTo(Organismcontrolle::class,'organisme_control_id','id');
    }
}
