<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Controlleur extends Model
{
    use HasFactory;

    public function production(){
        return $this->hasMany(Production::class);
    }

    public function organismcontrolle(){
        return $this->belongsTo(Organismcontrolle::class,'organismcontrolle_id','id');
    }
}
