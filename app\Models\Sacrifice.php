<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Sacrifice extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $fillable = [
        'reference',
        'controlleur_id',
        'societe_nettoyage_id',
        'organisme_control_id',
        'abattoire_id',
        'compte_id',
        'date_sacrifice',
        'user_id',
        'approved',
        'reference_bete',
        'categorie',
        'classement',
        'race_bete',
        'age',
        'poids',
        'date_arrive',
        'num_lot_sacrifice',
        'num_lot_elevage',
        // Ajoutez d'autres champs fillable au besoin
    ];



    public function compte(){
        return $this->belongsTo(Compte::class,'compte_id','id');
    }

    public function controlleur(){
        return $this->belongsTo(Controlleur::class,'controlleur_id','id');
    }

    public function organisme_control(){
        return $this->belongsTo(Organismcontrolle::class,'organisme_control_id','id');
    }
}
