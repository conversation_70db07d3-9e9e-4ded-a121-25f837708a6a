<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Atelierconditionnement;
use App\Models\Controlleur;
use Illuminate\Http\Request;
use Yajra\DataTables\DataTables;

class AtelierconditionnementController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            $controlleurs = Atelierconditionnement::all();
            return DataTables::of($controlleurs)
                ->addColumn('actions', function (Atelierconditionnement $Atelierconditionnement) {
                    // Generate action button HTML with route
                    return '<a href="'.route('attelierconditionnements.show', $Atelierconditionnement->id).'" class="btn btn-sm btn-icon btn-bg-light btn-active-color-primary">
                <!--begin::Svg Icon | path: icons/duotune/arrows/arr064.svg-->
                <span class="svg-icon svg-icon-2">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <rect opacity="0.5" x="18" y="13" width="13" height="2" rx="1" transform="rotate(-180 18 13)" fill="currentColor"></rect>
                        <path d="M15.4343 12.5657L11.25 16.75C10.8358 17.1642 10.8358 17.8358 11.25 18.25C11.6642 18.6642 12.3358 18.6642 12.75 18.25L18.2929 12.7071C18.6834 12.3166 18.6834 11.6834 18.2929 11.2929L12.75 5.75C12.3358 5.33579 11.6642 5.33579 11.25 5.75C10.8358 6.16421 10.8358 6.83579 11.25 7.25L15.4343 11.4343C15.7467 11.7467 15.7467 12.2533 15.4343 12.5657Z" fill="currentColor"></path>
                    </svg>
                </span>
                <!--end::Svg Icon-->
                               </a>';
                })
                ->rawColumns(['actions'])
                ->toJson();
        }
        return view('admin.atelierconditionnement.index');
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $record = Atelierconditionnement::find($id);
        return view('admin.atelierconditionnement.show',compact('record'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $record = Atelierconditionnement::find($id);
        return view('admin.atelierconditionnement.edit',compact('record'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        // Step 2: Find the AtelierConditionnement
        $atelierConditionnement = AtelierConditionnement::findOrFail($id);

        // Step 3: Assign Request Data to AtelierConditionnement Attributes
        $atelierConditionnement->name = $request->name;
        $atelierConditionnement->tva = $request->tva;
        $atelierConditionnement->siret = $request->siret;
        $atelierConditionnement->telephone = $request->telephone;
        $atelierConditionnement->email = $request->email;
        $atelierConditionnement->adresse = $request->adresse;
        $atelierConditionnement->ville = $request->ville;
        $atelierConditionnement->pays = $request->pays;
        $atelierConditionnement->website = $request->website;
        if ($request->hasFile('image')) {
            $image = $request->file('image');
            $imageName = time() . '.' . $image->getClientOriginalExtension();
            $image->move(public_path('upload/atelierconditionnement'), $imageName);

            // Mettre à jour le chemin de l'image du produit
            $atelierConditionnement->image = 'upload/atelierconditionnement/' . $imageName;
        }
        //$atelierConditionnement->approved = $request->approved;

        // Step 4: Save the AtelierConditionnement
        $atelierConditionnement->save();


        return redirect()->route('attelierconditionnements.show',$atelierConditionnement->id);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        // Step 1: Find the AtelierConditionnement
        $atelierConditionnement = AtelierConditionnement::findOrFail($id);

        // Step 2: Delete the AtelierConditionnement
        $atelierConditionnement->delete();
        return redirect()->route('attelierconditionnements.index');
    }


    public function toggleApproved($id)
    {
        $record = Atelierconditionnement::findOrFail($id);
        $record->approved = !$record->approved;
        $record->save();

        return response()->json(['success' => true, 'approved' => $record->approved]);
    }
}
