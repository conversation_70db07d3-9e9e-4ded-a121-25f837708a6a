import { useEffect } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { getCurrentUser } from '../store/slices/authSlice'

export const useAuth = () => {
  const { user, token, isAuthenticated, isLoading, error } = useSelector(
    (state) => state.auth
  )

  return {
    user,
    token,
    isAuthenticated,
    isLoading,
    error,
    isAdmin: user?.role === 'admin',
    isCustomer: user?.role === 'customer',
  }
}

export const useAuthCheck = () => {
  const dispatch = useDispatch()
  const { token, isAuthenticated } = useSelector((state) => state.auth)

  useEffect(() => {
    // If we have a token but no user data, fetch the current user
    if (token && !isAuthenticated) {
      dispatch(getCurrentUser())
    }
  }, [dispatch, token, isAuthenticated])
}

export default useAuth
