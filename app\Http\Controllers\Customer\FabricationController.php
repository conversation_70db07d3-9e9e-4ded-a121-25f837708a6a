<?php

namespace App\Http\Controllers\Customer;

use App\Http\Controllers\Controller;
use App\Models\Fabrication;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class FabricationController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            $data = Fabrication::where('compte_id',Auth::user()->compte_id)->get();
            return datatables()->of($data)

                ->addColumn('controlleur_id', function (Fabrication $fabrication) {
                    // Check if 'controlleur' relationship exists before accessing 'nom'
                    return $fabrication->controlleur ? $fabrication->controlleur->nom : '';
                })
                ->addColumn('compte', function (Fabrication $fabrication) {
                    // Check if 'controlleur' relationship exists before accessing 'nom'
                    return $fabrication->compte ? $fabrication->compte->name : '';
                })
                ->addColumn('approved', function (Fabrication $fabrication) {
                    // Set badge text based on the value of 'approved'
                    $badge = $fabrication->approved == 1 ? '<span class="badge badge-light-success">Approuvé</span>' : '<span class="badge badge-light-danger">Non Approuvé</span>';
                    return $badge;
                })
                ->addColumn('actions', function (Fabrication $fabrication) {
                    // Generate action button HTML with route
                    return '<a href="'.route('fabrication.show', $fabrication->id).'" class="btn btn-sm btn-icon btn-bg-light btn-active-color-primary">
                <!--begin::Svg Icon | path: icons/duotune/arrows/arr064.svg-->
                <span class="svg-icon svg-icon-2">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <rect opacity="0.5" x="18" y="13" width="13" height="2" rx="1" transform="rotate(-180 18 13)" fill="currentColor"></rect>
                        <path d="M15.4343 12.5657L11.25 16.75C10.8358 17.1642 10.8358 17.8358 11.25 18.25C11.6642 18.6642 12.3358 18.6642 12.75 18.25L18.2929 12.7071C18.6834 12.3166 18.6834 11.6834 18.2929 11.2929L12.75 5.75C12.3358 5.33579 11.6642 5.33579 11.25 5.75C10.8358 6.16421 10.8358 6.83579 11.25 7.25L15.4343 11.4343C15.7467 11.7467 15.7467 12.2533 15.4343 12.5657Z" fill="currentColor"></path>
                    </svg>
                </span>
                <!--end::Svg Icon-->
                               </a>';
                })
                ->rawColumns(['approved','compte','actions'])
                ->toJson();
        }
        return view('customer.Fabrication.index');
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        return view('customer.Fabrication.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {

        // Get the current date in the format 'daymonthyear'
        $date = now()->format('dmY');

// Generate a unique code (e.g., random string or incrementing number)
        $code = Str::random(4); // You can change this to generate your code

// Combine the date and code to form the reference
        $reference = "F-{$date}{$code}";

// Check if the reference already exists in the database
        while (Fabrication::where('reference', $reference)->exists()) {
            // Regenerate reference until it's unique
            $code = Str::random(4);
            $reference = "F-{$date}{$code}";
        }

// Create a new Fabrication instance with the validated data
        $fabrication = new Fabrication();
        $fabrication->reference = $reference;
        $fabrication->controlleur_id = $request->controlleur_id;
        $fabrication->compte_id = $request->fabricant_id;
        $fabrication->date_fabrication = Carbon::createFromFormat('Y-m-d', $request->date_fabrication);
        $fabrication->user_id = Auth::user()->id;
        $fabrication->num_lot_decoupe = $request->num_lot_decoupe;
        $fabrication->num_lot_elevage = $request->num_lot_elevage;
        $fabrication->num_lot_production = $request->num_lot_production;
        $fabrication->num_lot_fabrication = $reference; // Assuming num_lot_fabrication should be the same as the reference
        $fabrication->produit = $request->produit;
        $fabrication->poids = $request->poids;
        $fabrication->quantite = $request->quantite;
        $fabrication->dlc = Carbon::createFromFormat('Y-m-d', $request->dlc);

// Save the fabrication
        $fabrication->save();

        return view('customer.Fabrication.index');

    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $record = Fabrication::find($id);
        return view('customer.Fabrication.show',compact('record'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $record = Fabrication::find($id);
        return view('customer.Fabrication.edit',compact('record'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        // Valider la requête
        $request->validate([
            'image' => 'required|image|mimes:jpeg,png,jpg|max:2048', // Ajuster les règles de validation selon vos besoins
        ]);

        // Récupérer le produit
        $record = Fabrication::findOrFail($id);

        // Enregistrer la nouvelle image
        if ($request->hasFile('image')) {
            $image = $request->file('image');
            $imageName = time() . '.' . $image->getClientOriginalExtension();
            $image->move(public_path('upload/fabrication'), $imageName);

            // Mettre à jour le chemin de l'image du produit
            $record->image = 'upload/fabrication/' . $imageName;
            $record->save();
        }

        return redirect()->back();

    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $record = Fabrication::findOrFail($id);
        $record->delete();
        return redirect()->route('home');
    }
}
