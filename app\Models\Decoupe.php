<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Decoupe extends Model
{
    use HasFactory;

    protected $fillable = [
        'reference',
        'controlleur_id',
        'compte_id',
        'organisme_control_id',
        'societe_nettoyage_id',
        'user_id',
        'approved',
        'code_produit',
        'produit',
        'poids',
        'num_lot_sacrifice_id',
        'num_lot_decoupe',
        'dlc'
    ];


    public function compte(){
        return $this->belongsTo(Compte::class);
    }

    public function controlleur(){
        return $this->belongsTo(Controlleur::class);
    }

    public function societe_nettoyage(){
        return $this->belongsTo(Societenettoyage::class,'societe_nettoyage_id','id');
    }
    public function organisme_control(){
        return $this->belongsTo(Organismcontrolle::class,'organisme_control_id','id');
    }
}
