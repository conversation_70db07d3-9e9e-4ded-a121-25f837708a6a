import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'

const initialState = {
  results: [],
  isLoading: false,
  error: null,
  query: '',
  filters: {
    type: 'all', // all, production, elevage, sacrifice, etc.
    dateRange: null,
    status: '',
  },
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0,
  },
  recentSearches: JSON.parse(localStorage.getItem('recentSearches') || '[]'),
}

// Placeholder thunks
export const globalSearch = createAsyncThunk(
  'search/globalSearch',
  async ({ query, filters, page = 1 }, { rejectWithValue }) => {
    try {
      // TODO: Implement with actual service
      return {
        data: [],
        pagination: { page, limit: 20, total: 0, totalPages: 0 },
        query,
      }
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Search failed')
    }
  }
)

export const searchByType = createAsyncThunk(
  'search/searchByType',
  async ({ type, query, filters, page = 1 }, { rejectWithValue }) => {
    try {
      // TODO: Implement with actual service
      return {
        data: [],
        pagination: { page, limit: 20, total: 0, totalPages: 0 },
        query,
        type,
      }
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Search failed')
    }
  }
)

const searchSlice = createSlice({
  name: 'search',
  initialState,
  reducers: {
    clearResults: (state) => {
      state.results = []
      state.query = ''
      state.error = null
    },
    setQuery: (state, action) => {
      state.query = action.payload
    },
    setFilters: (state, action) => {
      state.filters = { ...state.filters, ...action.payload }
    },
    clearFilters: (state) => {
      state.filters = {
        type: 'all',
        dateRange: null,
        status: '',
      }
    },
    setPagination: (state, action) => {
      state.pagination = { ...state.pagination, ...action.payload }
    },
    addRecentSearch: (state, action) => {
      const search = {
        query: action.payload,
        timestamp: new Date().toISOString(),
      }
      
      // Remove duplicate if exists
      state.recentSearches = state.recentSearches.filter(
        (item) => item.query !== search.query
      )
      
      // Add to beginning
      state.recentSearches.unshift(search)
      
      // Keep only last 10 searches
      if (state.recentSearches.length > 10) {
        state.recentSearches = state.recentSearches.slice(0, 10)
      }
      
      // Save to localStorage
      localStorage.setItem('recentSearches', JSON.stringify(state.recentSearches))
    },
    removeRecentSearch: (state, action) => {
      state.recentSearches = state.recentSearches.filter(
        (item) => item.query !== action.payload
      )
      localStorage.setItem('recentSearches', JSON.stringify(state.recentSearches))
    },
    clearRecentSearches: (state) => {
      state.recentSearches = []
      localStorage.removeItem('recentSearches')
    },
    clearError: (state) => {
      state.error = null
    },
  },
  extraReducers: (builder) => {
    builder
      // Global Search
      .addCase(globalSearch.pending, (state) => {
        state.isLoading = true
        state.error = null
      })
      .addCase(globalSearch.fulfilled, (state, action) => {
        state.isLoading = false
        state.results = action.payload.data
        state.pagination = action.payload.pagination
        state.query = action.payload.query
        state.error = null
      })
      .addCase(globalSearch.rejected, (state, action) => {
        state.isLoading = false
        state.error = action.payload
      })
      
      // Search by Type
      .addCase(searchByType.pending, (state) => {
        state.isLoading = true
        state.error = null
      })
      .addCase(searchByType.fulfilled, (state, action) => {
        state.isLoading = false
        state.results = action.payload.data
        state.pagination = action.payload.pagination
        state.query = action.payload.query
        state.filters.type = action.payload.type
        state.error = null
      })
      .addCase(searchByType.rejected, (state, action) => {
        state.isLoading = false
        state.error = action.payload
      })
  },
})

export const {
  clearResults,
  setQuery,
  setFilters,
  clearFilters,
  setPagination,
  addRecentSearch,
  removeRecentSearch,
  clearRecentSearches,
  clearError,
} = searchSlice.actions

export default searchSlice.reducer
