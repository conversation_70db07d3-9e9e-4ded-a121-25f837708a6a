import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'
import productionService from '../../services/productionService'

const initialState = {
  productions: [],
  currentProduction: null,
  isLoading: false,
  error: null,
  pagination: {
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
  },
  filters: {
    search: '',
    status: '',
    dateRange: null,
    compte: '',
  },
}

// Async thunks
export const fetchProductions = createAsyncThunk(
  'production/fetchProductions',
  async (params, { rejectWithValue }) => {
    try {
      const response = await productionService.getAll(params)
      return response
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch productions')
    }
  }
)

export const fetchProductionById = createAsyncThunk(
  'production/fetchProductionById',
  async (id, { rejectWithValue }) => {
    try {
      const response = await productionService.getById(id)
      return response
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch production')
    }
  }
)

export const createProduction = createAsyncThunk(
  'production/createProduction',
  async (productionData, { rejectWithValue }) => {
    try {
      const response = await productionService.create(productionData)
      return response
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to create production')
    }
  }
)

export const updateProduction = createAsyncThunk(
  'production/updateProduction',
  async ({ id, data }, { rejectWithValue }) => {
    try {
      const response = await productionService.update(id, data)
      return response
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update production')
    }
  }
)

export const deleteProduction = createAsyncThunk(
  'production/deleteProduction',
  async (id, { rejectWithValue }) => {
    try {
      await productionService.delete(id)
      return id
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to delete production')
    }
  }
)

export const approveProduction = createAsyncThunk(
  'production/approveProduction',
  async (id, { rejectWithValue }) => {
    try {
      const response = await productionService.approve(id)
      return response
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to approve production')
    }
  }
)

export const generateCertificate = createAsyncThunk(
  'production/generateCertificate',
  async (id, { rejectWithValue }) => {
    try {
      const response = await productionService.generateCertificate(id)
      return response
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to generate certificate')
    }
  }
)

const productionSlice = createSlice({
  name: 'production',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null
    },
    clearCurrentProduction: (state) => {
      state.currentProduction = null
    },
    setFilters: (state, action) => {
      state.filters = { ...state.filters, ...action.payload }
    },
    clearFilters: (state) => {
      state.filters = {
        search: '',
        status: '',
        dateRange: null,
        compte: '',
      }
    },
    setPagination: (state, action) => {
      state.pagination = { ...state.pagination, ...action.payload }
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch Productions
      .addCase(fetchProductions.pending, (state) => {
        state.isLoading = true
        state.error = null
      })
      .addCase(fetchProductions.fulfilled, (state, action) => {
        state.isLoading = false
        state.productions = action.payload.data
        state.pagination = action.payload.pagination
        state.error = null
      })
      .addCase(fetchProductions.rejected, (state, action) => {
        state.isLoading = false
        state.error = action.payload
      })
      
      // Fetch Production by ID
      .addCase(fetchProductionById.pending, (state) => {
        state.isLoading = true
        state.error = null
      })
      .addCase(fetchProductionById.fulfilled, (state, action) => {
        state.isLoading = false
        state.currentProduction = action.payload.data
        state.error = null
      })
      .addCase(fetchProductionById.rejected, (state, action) => {
        state.isLoading = false
        state.error = action.payload
      })
      
      // Create Production
      .addCase(createProduction.pending, (state) => {
        state.isLoading = true
        state.error = null
      })
      .addCase(createProduction.fulfilled, (state, action) => {
        state.isLoading = false
        state.productions.unshift(action.payload.data)
        state.error = null
      })
      .addCase(createProduction.rejected, (state, action) => {
        state.isLoading = false
        state.error = action.payload
      })
      
      // Update Production
      .addCase(updateProduction.pending, (state) => {
        state.isLoading = true
        state.error = null
      })
      .addCase(updateProduction.fulfilled, (state, action) => {
        state.isLoading = false
        const index = state.productions.findIndex(p => p._id === action.payload.data._id)
        if (index !== -1) {
          state.productions[index] = action.payload.data
        }
        state.currentProduction = action.payload.data
        state.error = null
      })
      .addCase(updateProduction.rejected, (state, action) => {
        state.isLoading = false
        state.error = action.payload
      })
      
      // Delete Production
      .addCase(deleteProduction.pending, (state) => {
        state.isLoading = true
        state.error = null
      })
      .addCase(deleteProduction.fulfilled, (state, action) => {
        state.isLoading = false
        state.productions = state.productions.filter(p => p._id !== action.payload)
        state.error = null
      })
      .addCase(deleteProduction.rejected, (state, action) => {
        state.isLoading = false
        state.error = action.payload
      })
      
      // Approve Production
      .addCase(approveProduction.fulfilled, (state, action) => {
        const index = state.productions.findIndex(p => p._id === action.payload.data._id)
        if (index !== -1) {
          state.productions[index] = action.payload.data
        }
        if (state.currentProduction?._id === action.payload.data._id) {
          state.currentProduction = action.payload.data
        }
      })
      .addCase(approveProduction.rejected, (state, action) => {
        state.error = action.payload
      })
      
      // Generate Certificate
      .addCase(generateCertificate.rejected, (state, action) => {
        state.error = action.payload
      })
  },
})

export const {
  clearError,
  clearCurrentProduction,
  setFilters,
  clearFilters,
  setPagination,
} = productionSlice.actions

export default productionSlice.reducer
