<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Conditionnement extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $fillable = [
        'reference',
        'compte_id',
        'controlleur_id',
        'date_conditionnement',
        'user_id',
        'approved',
        'num_lot_decoupe',
        'num_lot_elevage',
        'num_lot_production',
        'num_lot_fabrication',
        'code_produit',
        'produit',
        'num_lot_conditionnement',
        'poids',
        'quantite',
        'dlc',
    ];


    public function compte(){
        return $this->belongsTo(Compte::class);
    }

    public function controlleur(){
        return $this->belongsTo(Controlleur::class);
    }

    public function organisme_control(){
        return $this->belongsTo(Organismcontrolle::class,'organisme_control_id','id');
    }
}
