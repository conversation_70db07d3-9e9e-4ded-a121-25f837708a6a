import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'

const initialState = {
  fabrications: [],
  currentFabrication: null,
  isLoading: false,
  error: null,
  pagination: { page: 1, limit: 10, total: 0, totalPages: 0 },
  filters: { search: '', status: '', dateRange: null, compte: '' },
}

// Placeholder thunks
export const fetchFabrications = createAsyncThunk('fabrication/fetchFabrications', async () => ({ data: [], pagination: initialState.pagination }))
export const fetchFabricationById = createAsyncThunk('fabrication/fetchFabricationById', async () => ({ data: null }))
export const createFabrication = createAsyncThunk('fabrication/createFabrication', async (data) => ({ data }))
export const updateFabrication = createAsyncThunk('fabrication/updateFabrication', async ({ id, data }) => ({ data: { ...data, _id: id } }))
export const deleteFabrication = createAsyncThunk('fabrication/deleteFabrication', async (id) => id)

const fabricationSlice = createSlice({
  name: 'fabrication',
  initialState,
  reducers: {
    clearError: (state) => { state.error = null },
    clearCurrentFabrication: (state) => { state.currentFabrication = null },
    setFilters: (state, action) => { state.filters = { ...state.filters, ...action.payload } },
    clearFilters: (state) => { state.filters = { search: '', status: '', dateRange: null, compte: '' } },
    setPagination: (state, action) => { state.pagination = { ...state.pagination, ...action.payload } },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchFabrications.pending, (state) => { state.isLoading = true })
      .addCase(fetchFabrications.fulfilled, (state, action) => { state.isLoading = false; state.fabrications = action.payload.data })
      .addCase(fetchFabricationById.fulfilled, (state, action) => { state.currentFabrication = action.payload.data })
      .addCase(createFabrication.fulfilled, (state, action) => { state.fabrications.unshift(action.payload.data) })
      .addCase(updateFabrication.fulfilled, (state, action) => {
        const index = state.fabrications.findIndex(f => f._id === action.payload.data._id)
        if (index !== -1) state.fabrications[index] = action.payload.data
        state.currentFabrication = action.payload.data
      })
      .addCase(deleteFabrication.fulfilled, (state, action) => {
        state.fabrications = state.fabrications.filter(f => f._id !== action.payload)
      })
  },
})

export const { clearError, clearCurrentFabrication, setFilters, clearFilters, setPagination } = fabricationSlice.actions
export default fabricationSlice.reducer
