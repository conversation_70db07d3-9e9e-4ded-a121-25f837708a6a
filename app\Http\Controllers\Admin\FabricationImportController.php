<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Fabrication;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\Session;

class FabricationImportController extends Controller
{
    public function showForm()
    {
        return view('admin.Fabrication.import.upload');
    }

    public function upload(Request $request)
    {
        $request->validate([
            'file' => 'required|file|mimes:xlsx,csv',
        ]);

        $path = $request->file('file')->store('temp');
        $data = Excel::toArray([], storage_path("app/$path"));

        Session::put('import_fabrication_path', $path);
        Session::put('import_fabrication_data', $data[0]);

        return view('admin.fabrication.import.preview', ['rows' => $data[0]]);
    }

    public function importConfirm(Request $request)
    {
        $rows = Session::get('import_fabrication_data');

        foreach ($rows as $index => $row) {
            if ($index === 0) continue; // skip headers

            Fabrication::create([
                'reference' => $row[0],
                'controlleur_id' => $row[1],
                'organisme_control_id' => $row[2],
                'compte_id' => $row[3],
                'date_fabrication' => $row[4],
                'user_id' => $row[5],
                'approved' => $row[6] ?? 0,
                'num_lot_decoupe' => $row[7] ?? null,
                'num_lot_elevage' => $row[8] ?? null,
                'num_lot_production' => $row[9] ?? null,
                'num_lot_fabrication' => $row[10] ?? null,
                'produit' => $row[11],
                'image' => $row[12] ?? null,
                'poids' => $row[13],
                'quantite' => $row[14],
                'dlc' => $row[15],
            ]);
        }

        Session::forget(['import_fabrication_data', 'import_fabrication_path']);

        return redirect()->route('fabrication.index')->with('success', 'Importation des fabrications réussie !');
    }
}
