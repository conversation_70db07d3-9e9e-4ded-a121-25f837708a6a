<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Conditionnement;
use App\Models\Decoupe;
use App\Models\Logistique;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class LogistiqueController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            $data = Logistique::all();
            return datatables()->of($data)
                ->addColumn('controlleur_id', function (Logistique $logistique) {
                    // Check if 'controlleur' relationship exists before accessing 'nom'
                    return $logistique->controlleur ? $logistique->controlleur->nom : '';
                })
                ->addColumn('compte', function (Logistique $logistique) {
                    // Check if 'controlleur' relationship exists before accessing 'nom'
                    return $logistique->compte ? $logistique->compte->name : '';
                })
                ->addColumn('approved', function (Logistique $logistique) {
                    // Set badge text based on the value of 'approved'
                    $badge = $logistique->approved == 1 ? '<span class="badge badge-light-success">Approuvé</span>' : '<span class="badge badge-light-danger">Non Approuvé</span>';
                    return $badge;
                })
                ->addColumn('actions', function (Logistique $logistique) {
                    // Generate action button HTML with route
                    return '<a href="'.route('logistiques.show', $logistique->id).'" class="btn btn-sm btn-icon btn-bg-light btn-active-color-primary">
                <!--begin::Svg Icon | path: icons/duotune/arrows/arr064.svg-->
                <span class="svg-icon svg-icon-2">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <rect opacity="0.5" x="18" y="13" width="13" height="2" rx="1" transform="rotate(-180 18 13)" fill="currentColor"></rect>
                        <path d="M15.4343 12.5657L11.25 16.75C10.8358 17.1642 10.8358 17.8358 11.25 18.25C11.6642 18.6642 12.3358 18.6642 12.75 18.25L18.2929 12.7071C18.6834 12.3166 18.6834 11.6834 18.2929 11.2929L12.75 5.75C12.3358 5.33579 11.6642 5.33579 11.25 5.75C10.8358 6.16421 10.8358 6.83579 11.25 7.25L15.4343 11.4343C15.7467 11.7467 15.7467 12.2533 15.4343 12.5657Z" fill="currentColor"></path>
                    </svg>
                </span>
                <!--end::Svg Icon-->
                               </a>';
                })
                ->rawColumns(['approved','compte','actions'])
                ->toJson();
        }
        return view('admin.Logistique.index');
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        return view('admin.Logistique.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $date = now()->format('dmY');
        $code = Str::random(4);
        $reference = "C-{$date}{$code}";

        while (Logistique::where('reference', $reference)->exists()) {
            $code = Str::random(4);
            $reference = "C-{$date}{$code}";
        }


        $record = new Logistique();
        $record->compte_id = $request->input('compte_id');
        $record->reference = $reference;
        $record->controlleur_id = $request->input('controlleur_id');
        $record->organisme_control_id = $request->input('organisme_control_id');
        $record->user_id = auth()->user()->id;
        $record->du = Carbon::createFromFormat('Y-m-d', $request->du);
        $record->au = Carbon::createFromFormat('Y-m-d', $request->au);
        $record->approved = $request->input('approved', false);
        $record->num_lot_production = $request->input('num_lot_production');
        $record->num_lot_elevage = $request->input('num_lot_elevage');
        $record->num_lot_decoupe = $request->input('num_lot_decoupe');
        $record->num_lot_sacrifice = $request->input('num_lot_sacrifice');
        $record->num_lot_fabrication = $request->input('num_lot_fabrication');
        $record->code_produit = $request->input('code_produit');
        //$record->image = $request->input('image');
        $record->code_produit = $request->input('code_produit');
        $record->produit = $request->input('produit');
        $record->poids = $request->input('poids');
        $record->quantite = $request->input('quantite');
        $record->num_lot_logistique = $request->input('num_lot_logistique');
        $record->temperature = $request->input('temperature');

        // Save the logistique
        $record->save();

        return redirect()->route('logistiques.show',$record->id);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $record = Logistique::findOrFail($id);
        return view('admin.Logistique.show',compact('record'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $record = Logistique::findOrFail($id);
        return view('admin.Logistique.edit',compact('record'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $record = Logistique::findOrFail($id);
        $record->compte_id = $request->input('compte_id');
        //$record->reference = $reference;
        $record->controlleur_id = $request->input('controlleur_id');
        $record->organisme_control_id = $request->input('organisme_control_id');
        $record->user_id = auth()->user()->id;
        $record->du = Carbon::createFromFormat('Y-m-d', $request->du);
        $record->au = Carbon::createFromFormat('Y-m-d', $request->au);
        $record->approved = $request->input('approved', false);
        $record->num_lot_production = $request->input('num_lot_production');
        $record->num_lot_elevage = $request->input('num_lot_elevage');
        $record->num_lot_decoupe = $request->input('num_lot_decoupe');
        $record->num_lot_sacrifice = $request->input('num_lot_sacrifice');
        $record->num_lot_fabrication = $request->input('num_lot_fabrication');
        $record->code_produit = $request->input('code_produit');
        //$record->image = $request->input('image');
        $record->code_produit = $request->input('code_produit');
        $record->produit = $request->input('produit');
        $record->poids = $request->input('poids');
        $record->quantite = $request->input('quantite');
        $record->num_lot_logistique = $request->input('num_lot_logistique');
        $record->temperature = $request->input('temperature');
        if ($request->hasFile('image')) {
            $image = $request->file('image');
            $imageName = time() . '.' . $image->getClientOriginalExtension();
            $image->move(public_path('upload/logistique'), $imageName);

            // Mettre à jour le chemin de l'image du produit
            $record->image = 'upload/logistique/' . $imageName;
        }
        // Save the logistique
        $record->save();

        return redirect()->route('logistiques.show',$record->id);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $record = Logistique::findOrFail($id);
        $record->delete();

        return redirect()->route('logistiques.index');
    }

    public function toggleApproved($id)
    {
        $production = Logistique::findOrFail($id);
        $production->approved = !$production->approved;
        $production->save();

        return response()->json(['success' => true, 'approved' => $production->approved]);
    }


    public function showCertificat($id)
    {
        $record = Logistique::find($id);
        return view('admin.Logistique.print',compact('record'));
    }
}
