<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Elevage;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\Session;

class LogistiqueImportController extends Controller
{
    public function showForm()
    {
        return view('admin.elevage.import.upload');
    }

    public function upload(Request $request)
    {
        $request->validate([
            'file' => 'required|file|mimes:xlsx,csv',
        ]);

        $path = $request->file('file')->store('temp');
        $data = Excel::toArray([], storage_path("app/$path"));

        Session::put('import_elevage_path', $path);
        Session::put('import_elevage_data', $data[0]);

        return view('admin.Elevage.import.preview', ['rows' => $data[0]]);
    }

    public function importConfirm(Request $request)
    {
        $rows = Session::get('import_elevage_data');

        foreach ($rows as $index => $row) {
            if ($index === 0) continue; // skip headers

            Elevage::create([
                'reference' => $row[0],
                'compte_id' => $row[1],
                'controlleur_id' => $row[2],
                'organisme_control_id' => $row[3],
                'date_debut' => $row[4],
                'date_fin' => $row[5],
                'user_id' => $row[6],
                'approved' => $row[7] ?? 0,
                'image' => $row[8],
                'produit' => $row[9],
                'origine' => $row[10],
                'poids' => $row[11],
                'quantite' => $row[12],
                'age' => $row[13],
                'num_lot_elevage' => $row[14],
                'num_lot_production' => $row[15],
            ]);
        }

        Session::forget(['import_elevage_data', 'import_elevage_path']);
        return redirect()->route('elevage.index')->with('success', 'Importation réussie !');
    }
}
