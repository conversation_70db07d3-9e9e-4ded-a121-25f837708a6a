<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Comment;
use Illuminate\Support\Facades\Storage;


class CommentController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $validatedData = $request->validate([
            'content' => 'nullable|string',
            'media_type' => 'nullable|string|in:image,video',
            'media_file' => 'nullable|file|mimes:jpeg,png,jpg,gif,mp4,mov,avi,flv|max:20480', // Accept image and video files up to 20MB
            'production' => 'nullable|string',
            'elevage' => 'nullable|string',
            'sacrifice' => 'nullable|string',
            'decoupe' => 'nullable|string',
            'fabrication' => 'nullable|string',
            'logistique' => 'nullable|string',
            'conditionnement' => 'nullable|string',
        ]);

        // Enregistrer le nouveau fichier média
        if ($request->hasFile('media_file')) {
            $mediaFile = $request->file('media_file');
            $mediaName = time() . '.' . $mediaFile->getClientOriginalExtension();
            $mediaFile->move(public_path('upload/comments'), $mediaName);

            // Mettre à jour le chemin du média
            $validatedData['media_url'] = 'upload/comments/' . $mediaName;
        }

        $comment = Comment::create($validatedData);

        return redirect()->back();
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        $validatedData = $request->validate([
            'content' => 'nullable|string',
            'media_type' => 'nullable|string|in:image,video',
            'media_file' => 'nullable|file|mimes:jpeg,png,jpg,gif,mp4,mov,avi,flv|max:20480',
        ]);

        $comment = Comment::findOrFail($id);

        if ($request->hasFile('media_file')) {
            $mediaFile = $request->file('media_file');
            $mediaName = time() . '.' . $mediaFile->getClientOriginalExtension();
            $mediaFile->move(public_path('upload/comments'), $mediaName);

            // Delete old media file if exists
            if ($comment->media_url && file_exists(public_path($comment->media_url))) {
                unlink(public_path($comment->media_url));
            }

            $validatedData['media_url'] = 'upload/comments/' . $mediaName;
        }

        $comment->update($validatedData);

        return redirect()->back()->with('success', 'Commentaire mis à jour avec succès.');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        $record = Comment::find($id);
        $record->delete();
        return redirect()->back();
    }
}
