import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'

const initialState = {
  sacrifices: [],
  currentSacrifice: null,
  isLoading: false,
  error: null,
  pagination: { page: 1, limit: 10, total: 0, totalPages: 0 },
  filters: { search: '', status: '', dateRange: null, compte: '' },
}

// Placeholder thunks
export const fetchSacrifices = createAsyncThunk('sacrifice/fetchSacrifices', async () => ({ data: [], pagination: initialState.pagination }))
export const fetchSacrificeById = createAsyncThunk('sacrifice/fetchSacrificeById', async () => ({ data: null }))
export const createSacrifice = createAsyncThunk('sacrifice/createSacrifice', async (data) => ({ data }))
export const updateSacrifice = createAsyncThunk('sacrifice/updateSacrifice', async ({ id, data }) => ({ data: { ...data, _id: id } }))
export const deleteSacrifice = createAsyncThunk('sacrifice/deleteSacrifice', async (id) => id)

const sacrificeSlice = createSlice({
  name: 'sacrifice',
  initialState,
  reducers: {
    clearError: (state) => { state.error = null },
    clearCurrentSacrifice: (state) => { state.currentSacrifice = null },
    setFilters: (state, action) => { state.filters = { ...state.filters, ...action.payload } },
    clearFilters: (state) => { state.filters = { search: '', status: '', dateRange: null, compte: '' } },
    setPagination: (state, action) => { state.pagination = { ...state.pagination, ...action.payload } },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchSacrifices.pending, (state) => { state.isLoading = true })
      .addCase(fetchSacrifices.fulfilled, (state, action) => { state.isLoading = false; state.sacrifices = action.payload.data })
      .addCase(fetchSacrificeById.fulfilled, (state, action) => { state.currentSacrifice = action.payload.data })
      .addCase(createSacrifice.fulfilled, (state, action) => { state.sacrifices.unshift(action.payload.data) })
      .addCase(updateSacrifice.fulfilled, (state, action) => {
        const index = state.sacrifices.findIndex(s => s._id === action.payload.data._id)
        if (index !== -1) state.sacrifices[index] = action.payload.data
        state.currentSacrifice = action.payload.data
      })
      .addCase(deleteSacrifice.fulfilled, (state, action) => {
        state.sacrifices = state.sacrifices.filter(s => s._id !== action.payload)
      })
  },
})

export const { clearError, clearCurrentSacrifice, setFilters, clearFilters, setPagination } = sacrificeSlice.actions
export default sacrificeSlice.reducer
