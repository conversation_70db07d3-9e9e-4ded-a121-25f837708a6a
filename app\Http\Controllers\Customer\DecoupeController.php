<?php

namespace App\Http\Controllers\Customer;

use App\Http\Controllers\Controller;
use App\Models\Decoupe;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class DecoupeController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            $data = Decoupe::where('compte_id',Auth::user()->compte_id)->get();
            return datatables()->of($data)
                ->addColumn('controlleur_id', function (Decoupe $decoupe) {
                    // Check if 'controlleur' relationship exists before accessing 'nom'
                    return $decoupe->controlleur ? $decoupe->controlleur->nom : '';
                })
                ->addColumn('compte_id', function (Decoupe $decoupe) {
                    // Check if 'controlleur' relationship exists before accessing 'nom'
                    return $decoupe->compte ? $decoupe->compte->name : '';
                })
                ->addColumn('approved', function (Decoupe $decoupe) {
                    // Set badge text based on the value of 'approved'
                    $badge = $decoupe->approved == 1 ? '<span class="badge badge-light-success">Approuvé</span>' : '<span class="badge badge-light-danger">Non Approuvé</span>';
                    return $badge;
                })
                ->addColumn('actions', function (Decoupe $decoupe) {
                    // Generate action button HTML with route
                    return '<a href="'.route('decoupe.show', $decoupe->id).'" class="btn btn-sm btn-icon btn-bg-light btn-active-color-primary">
                <!--begin::Svg Icon | path: icons/duotune/arrows/arr064.svg-->
                <span class="svg-icon svg-icon-2">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <rect opacity="0.5" x="18" y="13" width="13" height="2" rx="1" transform="rotate(-180 18 13)" fill="currentColor"></rect>
                        <path d="M15.4343 12.5657L11.25 16.75C10.8358 17.1642 10.8358 17.8358 11.25 18.25C11.6642 18.6642 12.3358 18.6642 12.75 18.25L18.2929 12.7071C18.6834 12.3166 18.6834 11.6834 18.2929 11.2929L12.75 5.75C12.3358 5.33579 11.6642 5.33579 11.25 5.75C10.8358 6.16421 10.8358 6.83579 11.25 7.25L15.4343 11.4343C15.7467 11.7467 15.7467 12.2533 15.4343 12.5657Z" fill="currentColor"></path>
                    </svg>
                </span>
                <!--end::Svg Icon-->
                               </a>';
                })
                ->rawColumns(['approved','actions'])
                ->toJson();
        }
        return view('customer.Decoupe.index');
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        return view('customer.Decoupe.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        // Get the current date in the format 'daymonthyear'
        $date = now()->format('dmY');

        // Generate a unique code (e.g., random string or incrementing number)
        $code = Str::random(4); // You can change this to generate your code

        // Combine the date and code to form the reference
        $reference = "E-{$date}{$code}";
        //dd($reference);
        // Create a new Production instance with the generated reference
        $record = new Decoupe([
            'reference' => $reference,
            'controlleur_id' => $request->controlleur_id,
            'compte_id' => $request->atelier_decoupe_id,
            'organisme_control_id' => $request->organisme_control_id,
            'societe_nettoyage_id' => $request->societe_nettoyage_id,
            'user_id' => Auth::user()->id,
            'code_produit' => $request->code_produit,
            'produit' => $request->produit,
            'poids' => $request->poids,
            'num_lot_sacrifice_id' => $request->num_lot_sacrifice_id,
            'num_lot_decoupe' => $request->num_lot_decoupe,
            'dlc' => Carbon::createFromFormat('Y-m-d', $request->dlc),

        ]);

        // Save the production
        $record->save();
        return redirect()->route('decoupe.show',$record->id);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $record = Decoupe::find($id);
        return view('customer.Decoupe.show',compact('record'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $record = Decoupe::find($id);
        return view('customer.Decoupe.edit',compact('record'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        // Valider la requête
        $request->validate([
            'image' => 'required|image|mimes:jpeg,png,jpg|max:2048', // Ajuster les règles de validation selon vos besoins
        ]);

        // Récupérer le produit
        $record = Decoupe::findOrFail($id);

        // Enregistrer la nouvelle image
        if ($request->hasFile('image')) {
            $image = $request->file('image');
            $imageName = time() . '.' . $image->getClientOriginalExtension();
            $image->move(public_path('upload/decoupe'), $imageName);

            // Mettre à jour le chemin de l'image du produit
            $record->image = 'upload/decoupe/' . $imageName;
            $record->save();
        }

        return redirect()->back();
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}
