import { apiGet, apiPost, apiPut } from './api'

const authService = {
  // Login user
  login: async (email, password) => {
    const response = await apiPost('/auth/login', { email, password })
    return response
  },

  // Register user
  register: async (userData) => {
    const response = await apiPost('/auth/register', userData)
    return response
  },

  // Logout user
  logout: async () => {
    const response = await apiPost('/auth/logout')
    return response
  },

  // Get current user
  getCurrentUser: async () => {
    const response = await apiGet('/auth/me')
    return response
  },

  // Update user profile
  updateProfile: async (userData) => {
    const response = await apiPut('/auth/profile', userData)
    return response
  },

  // Change password
  changePassword: async (currentPassword, newPassword) => {
    const response = await apiPut('/auth/change-password', {
      currentPassword,
      newPassword,
    })
    return response
  },

  // Forgot password
  forgotPassword: async (email) => {
    const response = await apiPost('/auth/forgot-password', { email })
    return response
  },

  // Reset password
  resetPassword: async (token, password) => {
    const response = await apiPost('/auth/reset-password', { token, password })
    return response
  },

  // Refresh token
  refreshToken: async (refreshToken) => {
    const response = await apiPost('/auth/refresh-token', { refreshToken })
    return response
  },
}

export default authService
