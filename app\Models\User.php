<?php

namespace App\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Fortify\TwoFactorAuthenticatable;
use <PERSON><PERSON>\Jetstream\HasProfilePhoto;
use Lara<PERSON>\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    use HasApiTokens;
    use HasFactory;
    use HasProfilePhoto;
    use Notifiable;
    use TwoFactorAuthenticatable;




    /**
     * The attributes that are mass assignable.
     *
     * @var string[]
     */
    protected $fillable = [
        'name',
        'email',
        'password',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array
     */
    protected $hidden = [
        'password',
        'remember_token',
        'two_factor_recovery_codes',
        'two_factor_secret',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
    ];

    /**
     * The accessors to append to the model's array form.
     *
     * @var array
     */
    protected $appends = [
        'profile_photo_url',
    ];


    // Your model properties and methods...

    // Define isAdmin method
    public function isAdmin()
    {
        // Implement the logic to determine if the user is an admin
        return $this->role === 'admin'; // Adjust the condition based on your role structure
    }

    // Define isCustomer method if needed
    public function isCustomer()
    {
        // Implement the logic to determine if the user is a customer
        return $this->role === 'customer'; // Adjust the condition based on your role structure
    }

    public function compte()
    {
        return $this->belongsTo(Compte::class);
    }
}
