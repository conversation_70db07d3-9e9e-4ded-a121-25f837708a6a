import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'

const initialState = {
  decoupes: [],
  currentDecoupe: null,
  isLoading: false,
  error: null,
  pagination: { page: 1, limit: 10, total: 0, totalPages: 0 },
  filters: { search: '', status: '', dateRange: null, compte: '' },
}

// Placeholder thunks
export const fetchDecoupes = createAsyncThunk('decoupe/fetchDecoupes', async () => ({ data: [], pagination: initialState.pagination }))
export const fetchDecoupeById = createAsyncThunk('decoupe/fetchDecoupeById', async () => ({ data: null }))
export const createDecoupe = createAsyncThunk('decoupe/createDecoupe', async (data) => ({ data }))
export const updateDecoupe = createAsyncThunk('decoupe/updateDecoupe', async ({ id, data }) => ({ data: { ...data, _id: id } }))
export const deleteDecoupe = createAsyncThunk('decoupe/deleteDecoupe', async (id) => id)

const decoupeSlice = createSlice({
  name: 'decoupe',
  initialState,
  reducers: {
    clearError: (state) => { state.error = null },
    clearCurrentDecoupe: (state) => { state.currentDecoupe = null },
    setFilters: (state, action) => { state.filters = { ...state.filters, ...action.payload } },
    clearFilters: (state) => { state.filters = { search: '', status: '', dateRange: null, compte: '' } },
    setPagination: (state, action) => { state.pagination = { ...state.pagination, ...action.payload } },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchDecoupes.pending, (state) => { state.isLoading = true })
      .addCase(fetchDecoupes.fulfilled, (state, action) => { state.isLoading = false; state.decoupes = action.payload.data })
      .addCase(fetchDecoupeById.fulfilled, (state, action) => { state.currentDecoupe = action.payload.data })
      .addCase(createDecoupe.fulfilled, (state, action) => { state.decoupes.unshift(action.payload.data) })
      .addCase(updateDecoupe.fulfilled, (state, action) => {
        const index = state.decoupes.findIndex(d => d._id === action.payload.data._id)
        if (index !== -1) state.decoupes[index] = action.payload.data
        state.currentDecoupe = action.payload.data
      })
      .addCase(deleteDecoupe.fulfilled, (state, action) => {
        state.decoupes = state.decoupes.filter(d => d._id !== action.payload)
      })
  },
})

export const { clearError, clearCurrentDecoupe, setFilters, clearFilters, setPagination } = decoupeSlice.actions
export default decoupeSlice.reducer
