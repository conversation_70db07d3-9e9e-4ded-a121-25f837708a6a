<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Decoupe;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\Session;

class DecoupeImportController extends Controller
{
    public function showForm()
    {
        return view('admin.Decoupe.import.upload');
    }

    public function upload(Request $request)
    {
        $request->validate([
            'file' => 'required|file|mimes:xlsx,csv',
        ]);

        $path = $request->file('file')->store('temp');
        $data = Excel::toArray([], storage_path("app/$path"));

        Session::put('import_decoupe_path', $path);
        Session::put('import_decoupe_data', $data[0]);

        return view('admin.Decoupe.import.preview', ['rows' => $data[0]]);
    }

    public function importConfirm(Request $request)
    {
        $rows = Session::get('import_decoupe_data');

        foreach ($rows as $index => $row) {
            if ($index === 0) continue; // skip header

            Decoupe::create([
                'reference' => $row[0],
                'controlleur_id' => $row[1],
                'compte_id' => $row[2],
                'organisme_control_id' => $row[3],
                'societe_nettoyage_id' => $row[4],
                'user_id' => $row[5],
                'approved' => $row[6] ?? 0,
                'code_produit' => $row[7],
                'image' => $row[8],
                'produit' => $row[9],
                'poids' => $row[10],
                'num_lot_sacrifice_id' => $row[11],
                'num_lot_decoupe' => $row[12],
                'dlc' => $row[13],
            ]);
        }

        Session::forget(['import_decoupe_data', 'import_decoupe_path']);

        return redirect()->route('decoupes.index')->with('success', 'Importation des découpes réussie !');
    }
}
