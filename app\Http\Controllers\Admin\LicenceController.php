<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Agrementp;
use App\Models\License;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;

class LicenceController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            $query = License::with('compte')->select('licenses.*');

            return DataTables::of($query)
                ->addColumn('compte', function (License $license) {
                    return $license->compte ? $license->compte->name : '';
                })
                ->addColumn('actions', function (License $license) {
                    return '
                    <a href="'.route('licenses.show', $license->id).'"
                       class="btn btn-sm btn-icon btn-bg-light btn-active-color-primary"
                       title="Afficher">
                        <i class="fas fa-eye"></i>
                    </a>';
                })
                ->rawColumns(['actions'])
                ->make(true);
        }

        return view('admin.licenses.index');
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        return view('admin.licenses.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        try {
            // ✅ Valider les données
            $request->validate([
                'compte_id' => 'nullable|string|max:255',
                'date_creation' => 'nullable|string',
                'date_expiration' => 'nullable|string',
                'auditeur' => 'nullable|string|max:255',
                'adresse_auditeur' => 'nullable|string|max:255',
            ]);

            // ✅ Conversion des dates en format Y-m-d (SQL)
            $dateCreation = $request->input('date_creation')
                ? Carbon::createFromFormat('d/m/Y', $request->input('date_creation'))->format('Y-m-d')
                : null;

            $dateExpiration = $request->input('date_expiration')
                ? Carbon::createFromFormat('d/m/Y', $request->input('date_expiration'))->format('Y-m-d')
                : null;

            // ✅ Création du modèle
            $license = new License();
            $license->compte_id = $request->input('compte_id');
            $license->numero = $request->input('numero');
            $license->date_creation = $dateCreation;
            $license->date_expiration = $dateExpiration;
            $license->auditeur = $request->input('auditeur');
            $license->adresse_auditeur = $request->input('adresse_auditeur');
            $license->save();

            return redirect()->route('licenses.index')->with('success', 'Licence ajoutée avec succès.');

        } catch (\Exception $e) {
            return back()->withInput()->withErrors([
                'error' => 'Une erreur est survenue : ' . $e->getMessage()
            ]);
        }
    }


    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        // Récupération de la licence avec la relation compte
        $record = License::with('compte')->findOrFail($id);

        // Retourne la vue avec les données
        return view('admin.licenses.show', compact('record'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        // 🔍 Récupérer la licence par ID ou renvoyer une 404 si elle n'existe pas
        $license = \App\Models\License::findOrFail($id);

        // 🔁 Récupérer les comptes pour le select si nécessaire
        $comptes = \App\Models\Compte::all();

        // 📄 Retourner la vue 'licenses.edit' avec les données
        return view('admin.licenses.edit', compact('license', 'comptes'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        try {
            // ✅ Valider les données
            $request->validate([
                'compte_id' => 'nullable|string|max:255',
                'date_creation' => 'nullable|string',
                'date_expiration' => 'nullable|string',
                'auditeur' => 'nullable|string|max:255',
                'adresse_auditeur' => 'nullable|string|max:255',
            ]);

            // ✅ Trouver le modèle existant
            $license = \App\Models\License::findOrFail($id);

            // ✅ Conversion des dates
            $dateCreation = $request->input('date_creation')
                ? Carbon::createFromFormat('d/m/Y', $request->input('date_creation'))->format('Y-m-d')
                : null;

            $dateExpiration = $request->input('date_expiration')
                ? Carbon::createFromFormat('d/m/Y', $request->input('date_expiration'))->format('Y-m-d')
                : null;

            // ✅ Mise à jour du modèle
            $license->compte_id = $request->input('compte_id');
            $license->numero = $request->input('numero');
            $license->date_creation = $dateCreation;
            $license->date_expiration = $dateExpiration;
            $license->auditeur = $request->input('auditeur');
            $license->adresse_auditeur = $request->input('adresse_auditeur');
            $license->save();

            return redirect()->route('licenses.index')->with('success', 'Licence mise à jour avec succès.');

        } catch (\Exception $e) {
            return back()->withInput()->withErrors([
                'error' => 'Une erreur est survenue : ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        try {
            $license = \App\Models\License::findOrFail($id);
            $license->delete();

            return redirect()->route('licenses.index')->with('success', 'Licence supprimée avec succès.');
        } catch (\Exception $e) {
            return redirect()->route('licenses.index')->withErrors([
                'error' => 'Une erreur est survenue lors de la suppression : ' . $e->getMessage()
            ]);
        }
    }


    public function print($id)
    {
        $record = License::findOrFail($id);
        return view('admin.licenses.print', compact('record'));

    }
}
