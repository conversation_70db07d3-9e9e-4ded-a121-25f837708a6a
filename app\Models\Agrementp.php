<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Agrementp extends Model
{
    use HasFactory;

    public function compte(){
        return $this->belongsTo(Compte::class);
    }

    use SoftDeletes;

    protected $table = 'agrementps';

    protected $fillable = [
        'compte_id',
        'produit',
        'numero',
    ];

}
