<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Logistique extends Model
{
    use HasFactory;
    use SoftDeletes;



    public function compte(){
        return $this->belongsTo(Compte::class);
    }

    public function controlleur(){
        return $this->belongsTo(Controlleur::class);
    }

    public function organisme_control(){
        return $this->belongsTo(Organismcontrolle::class,'organisme_control_id','id');
    }
}
