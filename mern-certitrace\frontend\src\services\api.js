import axios from 'axios'
import { store } from '../store/store'
import { clearCredentials, refreshToken } from '../store/slices/authSlice'
import toast from 'react-hot-toast'

// Create axios instance
const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL || 'http://localhost:5000/api/v1',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const state = store.getState()
    const token = state.auth.token

    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }

    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor to handle token refresh and errors
api.interceptors.response.use(
  (response) => {
    return response.data
  },
  async (error) => {
    const originalRequest = error.config

    // Handle 401 errors (unauthorized)
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true

      const state = store.getState()
      const refreshTokenValue = state.auth.refreshToken

      if (refreshTokenValue) {
        try {
          // Try to refresh the token
          await store.dispatch(refreshToken()).unwrap()
          
          // Retry the original request with new token
          const newState = store.getState()
          const newToken = newState.auth.token
          
          if (newToken) {
            originalRequest.headers.Authorization = `Bearer ${newToken}`
            return api(originalRequest)
          }
        } catch (refreshError) {
          // Refresh failed, logout user
          store.dispatch(clearCredentials())
          localStorage.removeItem('token')
          localStorage.removeItem('refreshToken')
          
          // Redirect to login page
          if (window.location.pathname !== '/auth/login') {
            window.location.href = '/auth/login'
          }
          
          return Promise.reject(refreshError)
        }
      } else {
        // No refresh token, logout user
        store.dispatch(clearCredentials())
        localStorage.removeItem('token')
        localStorage.removeItem('refreshToken')
        
        if (window.location.pathname !== '/auth/login') {
          window.location.href = '/auth/login'
        }
      }
    }

    // Handle other HTTP errors
    if (error.response) {
      const { status, data } = error.response

      switch (status) {
        case 400:
          toast.error(data.message || 'Bad request')
          break
        case 403:
          toast.error('Access denied')
          break
        case 404:
          toast.error('Resource not found')
          break
        case 422:
          // Validation errors
          if (data.errors && Array.isArray(data.errors)) {
            data.errors.forEach((err) => {
              toast.error(err.msg || err.message)
            })
          } else {
            toast.error(data.message || 'Validation error')
          }
          break
        case 429:
          toast.error('Too many requests. Please try again later.')
          break
        case 500:
          toast.error('Server error. Please try again later.')
          break
        default:
          toast.error(data.message || 'An error occurred')
      }
    } else if (error.request) {
      // Network error
      toast.error('Network error. Please check your connection.')
    } else {
      // Other error
      toast.error('An unexpected error occurred')
    }

    return Promise.reject(error)
  }
)

// Helper functions for different HTTP methods
export const apiGet = (url, config = {}) => api.get(url, config)
export const apiPost = (url, data = {}, config = {}) => api.post(url, data, config)
export const apiPut = (url, data = {}, config = {}) => api.put(url, data, config)
export const apiPatch = (url, data = {}, config = {}) => api.patch(url, data, config)
export const apiDelete = (url, config = {}) => api.delete(url, config)

// File upload helper
export const apiUpload = (url, formData, onUploadProgress = null) => {
  return api.post(url, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    onUploadProgress,
  })
}

// Download helper
export const apiDownload = async (url, filename) => {
  try {
    const response = await api.get(url, {
      responseType: 'blob',
    })

    // Create blob link to download
    const blob = new Blob([response.data])
    const link = document.createElement('a')
    link.href = window.URL.createObjectURL(blob)
    link.download = filename
    link.click()

    // Clean up
    window.URL.revokeObjectURL(link.href)
  } catch (error) {
    toast.error('Download failed')
    throw error
  }
}

export default api
