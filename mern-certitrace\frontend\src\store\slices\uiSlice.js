import { createSlice } from '@reduxjs/toolkit'

const initialState = {
  sidebarOpen: false,
  theme: 'light',
  loading: false,
  notifications: [],
  modals: {
    confirmDelete: {
      isOpen: false,
      title: '',
      message: '',
      onConfirm: null,
    },
    imagePreview: {
      isOpen: false,
      imageUrl: '',
      title: '',
    },
  },
  alerts: [],
}

const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    toggleSidebar: (state) => {
      state.sidebarOpen = !state.sidebarOpen
    },
    setSidebarOpen: (state, action) => {
      state.sidebarOpen = action.payload
    },
    setTheme: (state, action) => {
      state.theme = action.payload
      localStorage.setItem('theme', action.payload)
    },
    setLoading: (state, action) => {
      state.loading = action.payload
    },
    addNotification: (state, action) => {
      const notification = {
        id: Date.now(),
        timestamp: new Date().toISOString(),
        ...action.payload,
      }
      state.notifications.unshift(notification)
      // Keep only last 50 notifications
      if (state.notifications.length > 50) {
        state.notifications = state.notifications.slice(0, 50)
      }
    },
    removeNotification: (state, action) => {
      state.notifications = state.notifications.filter(
        (notification) => notification.id !== action.payload
      )
    },
    markNotificationAsRead: (state, action) => {
      const notification = state.notifications.find(
        (notification) => notification.id === action.payload
      )
      if (notification) {
        notification.read = true
      }
    },
    markAllNotificationsAsRead: (state) => {
      state.notifications.forEach((notification) => {
        notification.read = true
      })
    },
    clearNotifications: (state) => {
      state.notifications = []
    },
    openConfirmDeleteModal: (state, action) => {
      state.modals.confirmDelete = {
        isOpen: true,
        ...action.payload,
      }
    },
    closeConfirmDeleteModal: (state) => {
      state.modals.confirmDelete = {
        isOpen: false,
        title: '',
        message: '',
        onConfirm: null,
      }
    },
    openImagePreviewModal: (state, action) => {
      state.modals.imagePreview = {
        isOpen: true,
        ...action.payload,
      }
    },
    closeImagePreviewModal: (state) => {
      state.modals.imagePreview = {
        isOpen: false,
        imageUrl: '',
        title: '',
      }
    },
    addAlert: (state, action) => {
      const alert = {
        id: Date.now(),
        timestamp: new Date().toISOString(),
        ...action.payload,
      }
      state.alerts.push(alert)
    },
    removeAlert: (state, action) => {
      state.alerts = state.alerts.filter((alert) => alert.id !== action.payload)
    },
    clearAlerts: (state) => {
      state.alerts = []
    },
  },
})

export const {
  toggleSidebar,
  setSidebarOpen,
  setTheme,
  setLoading,
  addNotification,
  removeNotification,
  markNotificationAsRead,
  markAllNotificationsAsRead,
  clearNotifications,
  openConfirmDeleteModal,
  closeConfirmDeleteModal,
  openImagePreviewModal,
  closeImagePreviewModal,
  addAlert,
  removeAlert,
  clearAlerts,
} = uiSlice.actions

export default uiSlice.reducer
