import { createSlice, createAsyncThunk } from '@reduxjs/toolkit'
import elevageService from '../../services/elevageService'

const initialState = {
  elevages: [],
  currentElevage: null,
  isLoading: false,
  error: null,
  pagination: {
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
  },
  filters: {
    search: '',
    status: '',
    dateRange: null,
    compte: '',
  },
}

// Placeholder async thunks - to be implemented
export const fetchElevages = createAsyncThunk(
  'elevage/fetchElevages',
  async (params, { rejectWithValue }) => {
    try {
      // TODO: Implement with actual service
      return { data: [], pagination: initialState.pagination }
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch elevages')
    }
  }
)

export const fetchElevageById = createAsyncThunk(
  'elevage/fetchElevageById',
  async (id, { rejectWithValue }) => {
    try {
      // TODO: Implement with actual service
      return { data: null }
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch elevage')
    }
  }
)

export const createElevage = createAsyncThunk(
  'elevage/createElevage',
  async (elevageData, { rejectWithValue }) => {
    try {
      // TODO: Implement with actual service
      return { data: elevageData }
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to create elevage')
    }
  }
)

export const updateElevage = createAsyncThunk(
  'elevage/updateElevage',
  async ({ id, data }, { rejectWithValue }) => {
    try {
      // TODO: Implement with actual service
      return { data: { ...data, _id: id } }
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to update elevage')
    }
  }
)

export const deleteElevage = createAsyncThunk(
  'elevage/deleteElevage',
  async (id, { rejectWithValue }) => {
    try {
      // TODO: Implement with actual service
      return id
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to delete elevage')
    }
  }
)

const elevageSlice = createSlice({
  name: 'elevage',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null
    },
    clearCurrentElevage: (state) => {
      state.currentElevage = null
    },
    setFilters: (state, action) => {
      state.filters = { ...state.filters, ...action.payload }
    },
    clearFilters: (state) => {
      state.filters = {
        search: '',
        status: '',
        dateRange: null,
        compte: '',
      }
    },
    setPagination: (state, action) => {
      state.pagination = { ...state.pagination, ...action.payload }
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchElevages.pending, (state) => {
        state.isLoading = true
        state.error = null
      })
      .addCase(fetchElevages.fulfilled, (state, action) => {
        state.isLoading = false
        state.elevages = action.payload.data
        state.pagination = action.payload.pagination
        state.error = null
      })
      .addCase(fetchElevages.rejected, (state, action) => {
        state.isLoading = false
        state.error = action.payload
      })
      .addCase(fetchElevageById.fulfilled, (state, action) => {
        state.currentElevage = action.payload.data
      })
      .addCase(createElevage.fulfilled, (state, action) => {
        state.elevages.unshift(action.payload.data)
      })
      .addCase(updateElevage.fulfilled, (state, action) => {
        const index = state.elevages.findIndex(e => e._id === action.payload.data._id)
        if (index !== -1) {
          state.elevages[index] = action.payload.data
        }
        state.currentElevage = action.payload.data
      })
      .addCase(deleteElevage.fulfilled, (state, action) => {
        state.elevages = state.elevages.filter(e => e._id !== action.payload)
      })
  },
})

export const {
  clearError,
  clearCurrentElevage,
  setFilters,
  clearFilters,
  setPagination,
} = elevageSlice.actions

export default elevageSlice.reducer
