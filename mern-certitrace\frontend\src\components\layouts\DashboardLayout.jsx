import { Outlet, Link, useNavigate } from 'react-router-dom'
import { useDispatch } from 'react-redux'
import { useState } from 'react'
import { 
  Menu, 
  X, 
  Home, 
  Users, 
  Building2, 
  Package, 
  Beef, 
  Scissors, 
  Wrench, 
  Box, 
  Truck, 
  Search, 
  User, 
  LogOut,
  Settings
} from 'lucide-react'
import { useAuth } from '../../hooks/useAuth'
import { logout } from '../../store/slices/authSlice'
import toast from 'react-hot-toast'

const DashboardLayout = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const { user, isAdmin } = useAuth()
  const dispatch = useDispatch()
  const navigate = useNavigate()

  const handleLogout = async () => {
    try {
      await dispatch(logout()).unwrap()
      toast.success('Logged out successfully')
      navigate('/auth/login')
    } catch (error) {
      toast.error('Logout failed')
    }
  }

  const navigation = [
    {
      name: 'Dashboard',
      href: isAdmin ? '/dashboard/admin' : '/dashboard/customer',
      icon: Home,
      current: false,
    },
    ...(isAdmin ? [
      {
        name: 'Users',
        href: '/dashboard/admin/users',
        icon: Users,
        current: false,
      },
      {
        name: 'Companies',
        href: '/dashboard/admin/companies',
        icon: Building2,
        current: false,
      },
    ] : []),
    {
      name: 'Productions',
      href: '/dashboard/productions',
      icon: Package,
      current: false,
    },
    {
      name: 'Elevages',
      href: '/dashboard/elevages',
      icon: Beef,
      current: false,
    },
    {
      name: 'Sacrifices',
      href: '/dashboard/sacrifices',
      icon: Scissors,
      current: false,
    },
    {
      name: 'Decoupes',
      href: '/dashboard/decoupes',
      icon: Wrench,
      current: false,
    },
    {
      name: 'Fabrications',
      href: '/dashboard/fabrications',
      icon: Box,
      current: false,
    },
    {
      name: 'Conditionnements',
      href: '/dashboard/conditionnements',
      icon: Package,
      current: false,
    },
    {
      name: 'Logistiques',
      href: '/dashboard/logistiques',
      icon: Truck,
      current: false,
    },
  ]

  return (
    <div className="h-screen flex overflow-hidden bg-gray-100">
      {/* Sidebar */}
      <div className={`${sidebarOpen ? 'block' : 'hidden'} fixed inset-0 flex z-40 md:hidden`}>
        <div className="fixed inset-0 bg-gray-600 bg-opacity-75" onClick={() => setSidebarOpen(false)} />
        <div className="relative flex-1 flex flex-col max-w-xs w-full bg-white">
          <div className="absolute top-0 right-0 -mr-12 pt-2">
            <button
              className="ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white"
              onClick={() => setSidebarOpen(false)}
            >
              <X className="h-6 w-6 text-white" />
            </button>
          </div>
          <SidebarContent navigation={navigation} user={user} onLogout={handleLogout} />
        </div>
      </div>

      {/* Static sidebar for desktop */}
      <div className="hidden md:flex md:flex-shrink-0">
        <div className="flex flex-col w-64">
          <SidebarContent navigation={navigation} user={user} onLogout={handleLogout} />
        </div>
      </div>

      {/* Main content */}
      <div className="flex flex-col w-0 flex-1 overflow-hidden">
        {/* Top bar */}
        <div className="relative z-10 flex-shrink-0 flex h-16 bg-white shadow">
          <button
            className="px-4 border-r border-gray-200 text-gray-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500 md:hidden"
            onClick={() => setSidebarOpen(true)}
          >
            <Menu className="h-6 w-6" />
          </button>
          
          <div className="flex-1 px-4 flex justify-between">
            <div className="flex-1 flex">
              <div className="w-full flex md:ml-0">
                <div className="relative w-full text-gray-400 focus-within:text-gray-600">
                  <div className="absolute inset-y-0 left-0 flex items-center pointer-events-none">
                    <Search className="h-5 w-5" />
                  </div>
                  <input
                    className="block w-full h-full pl-8 pr-3 py-2 border-transparent text-gray-900 placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-0 focus:border-transparent sm:text-sm"
                    placeholder="Search..."
                    type="search"
                  />
                </div>
              </div>
            </div>
            
            <div className="ml-4 flex items-center md:ml-6">
              <div className="ml-3 relative">
                <div className="flex items-center space-x-3">
                  <span className="text-sm font-medium text-gray-700">
                    {user?.name}
                  </span>
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
                    {user?.role}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Page content */}
        <main className="flex-1 relative overflow-y-auto focus:outline-none">
          <div className="py-6">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
              <Outlet />
            </div>
          </div>
        </main>
      </div>
    </div>
  )
}

const SidebarContent = ({ navigation, user, onLogout }) => {
  return (
    <div className="flex flex-col h-full bg-white border-r border-gray-200">
      {/* Logo */}
      <div className="flex items-center h-16 flex-shrink-0 px-4 bg-primary-600">
        <img
          className="h-8 w-auto"
          src="/logo.svg"
          alt="CertiTrace"
        />
        <span className="ml-2 text-xl font-bold text-white">
          CertiTrace
        </span>
      </div>

      {/* Navigation */}
      <div className="flex-1 flex flex-col overflow-y-auto">
        <nav className="flex-1 px-2 py-4 space-y-1">
          {navigation.map((item) => (
            <Link
              key={item.name}
              to={item.href}
              className="text-gray-600 hover:bg-gray-50 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md"
            >
              <item.icon
                className="text-gray-400 group-hover:text-gray-500 mr-3 flex-shrink-0 h-6 w-6"
              />
              {item.name}
            </Link>
          ))}
        </nav>

        {/* User menu */}
        <div className="flex-shrink-0 border-t border-gray-200 p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="h-8 w-8 rounded-full bg-primary-100 flex items-center justify-center">
                <User className="h-5 w-5 text-primary-600" />
              </div>
            </div>
            <div className="ml-3 flex-1">
              <p className="text-sm font-medium text-gray-700">{user?.name}</p>
              <p className="text-xs text-gray-500">{user?.email}</p>
            </div>
          </div>
          
          <div className="mt-3 space-y-1">
            <Link
              to="/dashboard/profile"
              className="text-gray-600 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md"
            >
              <Settings className="text-gray-400 group-hover:text-gray-500 mr-3 h-4 w-4" />
              Profile
            </Link>
            <button
              onClick={onLogout}
              className="text-gray-600 hover:text-gray-900 group flex items-center px-2 py-2 text-sm font-medium rounded-md w-full text-left"
            >
              <LogOut className="text-gray-400 group-hover:text-gray-500 mr-3 h-4 w-4" />
              Logout
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default DashboardLayout
