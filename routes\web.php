<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('/loading', function () {
    return view('loading');
})->name('loading');

// Public routes accessible to everyone
Route::get('/', function () {
    return view('welcome');
})->name('home');

Route::get('/chart_qualite', function () {
    return view('chart_qualite');
})->name('charte-qualite');
// Add more public routes as needed
Route::post('home_search', [\App\Http\Controllers\SearchController::class, 'search'])->name('startsearch.search');
Route::get('home_search', [\App\Http\Controllers\SearchController::class, 'search'])->name('startsearch.search');

Route::resource('register_customer',\App\Http\Controllers\Customer\RegisterController::class);


Route::get('/productions-certificat/{id}', [\App\Http\Controllers\Admin\ProductionController::class, 'showCertificat'])->name('productions-certificat');
Route::get('/elevages-certificat/{id}', [\App\Http\Controllers\Admin\ElevageController::class, 'showCertificat'])->name('elevages-certificat');
Route::get('/sacrifices-certificat/{id}', [\App\Http\Controllers\Admin\SacrificeController::class, 'showCertificat'])->name('sacrifices-certificat');
Route::get('/decoupes-certificat/{id}', [\App\Http\Controllers\Admin\DecoupeController::class, 'showCertificat'])->name('decoupes-certificat');
Route::get('/fabrications-certificat/{id}', [\App\Http\Controllers\Admin\FabricationController::class, 'showCertificat'])->name('fabrications-certificat');
Route::get('/conditionnement-certificat/{id}', [\App\Http\Controllers\Admin\ConditionnementController::class, 'showCertificat'])->name('conditionnement-certificat');
Route::get('/logistique-certificat/{id}', [\App\Http\Controllers\Admin\LogistiqueController::class, 'showCertificat'])->name('logistique-certificat');


Route::get('/agrement_produit/{id}/print', [\App\Http\Controllers\Admin\AgrementpController::class, 'print'])->name('agrement_produit.print');
Route::get('/agrement_commerce/{id}/print', [\App\Http\Controllers\Admin\AgrementcController::class, 'print'])->name('agrement_commerce.print');
Route::get('/licenses/{id}/print', [\App\Http\Controllers\Admin\LicenceController::class, 'print'])->name('licenses.print');
Route::get('/sertificats/{id}/print', [\App\Http\Controllers\Admin\CertificatController::class, 'print'])->name('sertificats.print');


Route::middleware([
    'auth:sanctum',
    config('jetstream.auth_session'),
    'verified'
])->group(function () {

    // Backend routes accessible to backend users
    Route::middleware(['admin'])->group(function () {
        //Route::get('/dashboard', [App\Http\Controllers\Admin\AdminController::class], 'index')->name('admin.dashboard');
        //Route::resource('/admin/comptes', 'CompteController');
        Route::get('/dashboards', [App\Http\Controllers\Admin\AdminController::class, 'index'])->name('admin.dashboard');
        Route::resource('comptes',\App\Http\Controllers\Admin\CompteController::class);
        Route::resource('controlleurs',\App\Http\Controllers\Admin\ControlleurController::class);
        Route::resource('organismescontrolles',\App\Http\Controllers\Admin\OrganismcontrolleController::class);
        Route::resource('societedenettoyages',\App\Http\Controllers\Admin\SocietenettoyageController::class);
        Route::resource('attelierconditionnements',\App\Http\Controllers\Admin\AtelierconditionnementController::class);


        Route::resource('productions',\App\Http\Controllers\Admin\ProductionController::class);
        Route::resource('elevages',\App\Http\Controllers\Admin\ElevageController::class);
        Route::resource('sacrifices',\App\Http\Controllers\Admin\SacrificeController::class);
        Route::resource('decoupes',\App\Http\Controllers\Admin\DecoupeController::class);
        Route::resource('fabrications',\App\Http\Controllers\Admin\FabricationController::class);
        Route::resource('conditionnements',\App\Http\Controllers\Admin\ConditionnementController::class);
        Route::resource('logistiques',\App\Http\Controllers\Admin\LogistiqueController::class);


        Route::prefix('productions/import')->group(function () {
            Route::get('form', [\App\Http\Controllers\Admin\ProductionImportController::class, 'showForm'])->name('productions.upload.form');
            Route::post('upload', [\App\Http\Controllers\Admin\ProductionImportController::class, 'upload'])->name('productions.upload');
            Route::post('confirm', [\App\Http\Controllers\Admin\ProductionImportController::class, 'importConfirm'])->name('productions.import.confirm');
        });

        Route::prefix('elevages/import')->group(function () {
            Route::get('form', [\App\Http\Controllers\Admin\ElevageImportController::class, 'showForm'])->name('elevages.upload.form');
            Route::post('upload', [\App\Http\Controllers\Admin\ElevageImportController::class, 'upload'])->name('elevages.upload');
            Route::post('confirm', [\App\Http\Controllers\Admin\ElevageImportController::class, 'importConfirm'])->name('elevages.import.confirm');
        });

        Route::prefix('sacrifices/import')->group(function () {
            Route::get('form', [\App\Http\Controllers\Admin\SacrificeImportController::class, 'showForm'])->name('sacrifices.upload.form');
            Route::post('upload', [\App\Http\Controllers\Admin\SacrificeImportController::class, 'upload'])->name('sacrifices.upload');
            Route::post('confirm', [\App\Http\Controllers\Admin\SacrificeImportController::class, 'importConfirm'])->name('sacrifices.import.confirm');
        });

        Route::prefix('decoupes/import')->group(function () {
            Route::get('form', [\App\Http\Controllers\Admin\DecoupeImportController::class, 'showForm'])->name('decoupes.upload.form');
            Route::post('upload', [\App\Http\Controllers\Admin\DecoupeImportController::class, 'upload'])->name('decoupes.upload');
            Route::post('confirm', [\App\Http\Controllers\Admin\DecoupeImportController::class, 'importConfirm'])->name('decoupes.import.confirm');
        });

        Route::prefix('fabrication/import')->group(function () {
            Route::get('form', [\App\Http\Controllers\Admin\FabricationImportController::class, 'showForm'])->name('fabrication.upload.form');
            Route::post('upload', [\App\Http\Controllers\Admin\FabricationImportController::class, 'upload'])->name('fabrication.upload');
            Route::post('confirm', [\App\Http\Controllers\Admin\FabricationImportController::class, 'importConfirm'])->name('fabrication.import.confirm');
        });

        Route::prefix('logistique/import')->group(function () {
            Route::get('form', [\App\Http\Controllers\Admin\LogistiqueImportController::class, 'showForm'])->name('logistique.upload.form');
            Route::post('upload', [\App\Http\Controllers\Admin\LogistiqueImportController::class, 'upload'])->name('logistique.upload');
            Route::post('confirm', [\App\Http\Controllers\Admin\LogistiqueImportController::class, 'importConfirm'])->name('logistique.import.confirm');
        });

        Route::prefix('conditionnement/import')->group(function () {
            Route::get('form', [\App\Http\Controllers\Admin\ConditionnementImportController::class, 'showForm'])->name('conditionnement.upload.form');
            Route::post('upload', [\App\Http\Controllers\Admin\ConditionnementImportController::class, 'upload'])->name('conditionnement.upload');
            Route::post('confirm', [\App\Http\Controllers\Admin\ConditionnementImportController::class, 'importConfirm'])->name('conditionnement.import.confirm');
        });




        Route::get('productions/{production}/duplicate', [\App\Http\Controllers\Admin\ProductionController::class, 'duplicate'])->name('productions.duplicate');
        Route::get('elevages/{elevages}/duplicate', [\App\Http\Controllers\Admin\ElevageController::class, 'duplicate'])->name('elevages.duplicate');
        Route::get('sacrifices/{sacrifice}/duplicate', [\App\Http\Controllers\Admin\SacrificeController::class, 'duplicate'])->name('sacrifices.duplicate');
        Route::get('decoupes/{decoupe}/duplicate', [\App\Http\Controllers\Admin\DecoupeController::class, 'duplicate'])->name('decoupes.duplicate');
        Route::get('fabrications/{fabrication}/duplicate', [\App\Http\Controllers\Admin\FabricationController::class, 'duplicate'])->name('fabrications.duplicate');


        Route::resource('comments', \App\Http\Controllers\Admin\CommentController::class);


        Route::post('/comptes-approved/{id}', [\App\Http\Controllers\Admin\CompteController::class, 'toggleApproved'])->name('comptes-approved');
        Route::post('/attelierconditionnements-approved/{id}', [\App\Http\Controllers\Admin\AtelierconditionnementController::class, 'toggleApproved'])->name('attelierconditionnements-approved');
        Route::post('/productions-approved/{id}', [\App\Http\Controllers\Admin\ProductionController::class, 'toggleApproved'])->name('productions-approved');
        Route::post('/elevages-approved/{id}', [\App\Http\Controllers\Admin\ElevageController::class, 'toggleApproved'])->name('elevages-approved');
        Route::post('/sacrifices-approved/{id}', [\App\Http\Controllers\Admin\SacrificeController::class, 'toggleApproved'])->name('sacrifices-approved');
        Route::post('/decoupes-approved/{id}', [\App\Http\Controllers\Admin\DecoupeController::class, 'toggleApproved'])->name('decoupes-approved');
        Route::post('/fabrications-approved/{id}', [\App\Http\Controllers\Admin\FabricationController::class, 'toggleApproved'])->name('fabrications-approved');
        Route::post('/conditionnements-approved/{id}', [\App\Http\Controllers\Admin\ConditionnementController::class, 'toggleApproved'])->name('conditionnements-approved');
        Route::post('/logistiques-approved/{id}', [\App\Http\Controllers\Admin\LogistiqueController::class, 'toggleApproved'])->name('logistiques-approved');

        // Bureau CertiTRACE -------------

        Route::resource('agrement_commerce', \App\Http\Controllers\Admin\AgrementcController::class);
        Route::resource('agrement_produit',\App\Http\Controllers\Admin\AgrementpController::class);
        Route::resource('licenses',\App\Http\Controllers\Admin\LicenceController::class);
        Route::resource('sertificats',\App\Http\Controllers\Admin\CertificatController::class);




        // --------------------------------


        Route::resource('search_admin',\App\Http\Controllers\Admin\SearchController::class);
        Route::post('globalsearch_admin', [\App\Http\Controllers\Admin\SearchController::class, 'search'])->name('global_admin.search');
    });


    Route::middleware(['customer'])->group(function () {
        Route::get('/dashboard', [App\Http\Controllers\Customer\DashboardController::class, 'index'])->name('customer.dashboard');

        Route::resource('compte',\App\Http\Controllers\Customer\CompteController::class);
        Route::resource('controlleur',\App\Http\Controllers\Customer\ControlleurController::class);
        Route::resource('organismesdecontrolle',\App\Http\Controllers\Customer\OrganismedecontrolleController::class);
        Route::resource('societedenettoyage',\App\Http\Controllers\Customer\SocietenettoyageController::class);

        Route::resource('production',\App\Http\Controllers\Customer\ProductionController::class);
        Route::resource('elevage',\App\Http\Controllers\Customer\ElevageController::class);
        Route::resource('sacrifice',\App\Http\Controllers\Customer\SacrificeController::class);
        Route::resource('decoupe',\App\Http\Controllers\Customer\DecoupeController::class);
        Route::resource('fabrication',\App\Http\Controllers\Customer\FabricationController::class);
        Route::resource('conditionnement',\App\Http\Controllers\Customer\ConditionnementController::class);
        Route::resource('logistique',\App\Http\Controllers\Customer\LogistiqueController::class);

        Route::resource('search',\App\Http\Controllers\Customer\SearchController::class);
        Route::post('globalsearch', [\App\Http\Controllers\Customer\SearchController::class, 'search'])->name('global.search');


    });


});



