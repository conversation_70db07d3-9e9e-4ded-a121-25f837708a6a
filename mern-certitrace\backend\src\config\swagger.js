import swaggerJsdoc from 'swagger-jsdoc';
import swaggerUi from 'swagger-ui-express';

const options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'CertiTrace API',
      version: '1.0.0',
      description: 'API documentation for CertiTrace - Food Production Certification System',
      contact: {
        name: 'CertiTrace Team',
        email: '<EMAIL>',
      },
    },
    servers: [
      {
        url: `http://localhost:${process.env.PORT || 5000}/api/${process.env.API_VERSION || 'v1'}`,
        description: 'Development server',
      },
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
        },
      },
      schemas: {
        User: {
          type: 'object',
          required: ['name', 'email', 'password', 'role'],
          properties: {
            _id: {
              type: 'string',
              description: 'User ID',
            },
            name: {
              type: 'string',
              description: 'User full name',
            },
            email: {
              type: 'string',
              format: 'email',
              description: 'User email address',
            },
            role: {
              type: 'string',
              enum: ['admin', 'customer'],
              description: 'User role',
            },
            compte: {
              type: 'string',
              description: 'Associated company ID',
            },
            isActive: {
              type: 'boolean',
              description: 'User active status',
            },
            createdAt: {
              type: 'string',
              format: 'date-time',
            },
            updatedAt: {
              type: 'string',
              format: 'date-time',
            },
          },
        },
        Compte: {
          type: 'object',
          required: ['name', 'email'],
          properties: {
            _id: {
              type: 'string',
              description: 'Company ID',
            },
            name: {
              type: 'string',
              description: 'Company name',
            },
            email: {
              type: 'string',
              format: 'email',
              description: 'Company email',
            },
            telephone: {
              type: 'string',
              description: 'Phone number',
            },
            adresse: {
              type: 'string',
              description: 'Address',
            },
            ville: {
              type: 'string',
              description: 'City',
            },
            pays: {
              type: 'string',
              description: 'Country',
            },
            siret: {
              type: 'string',
              description: 'SIRET number',
            },
            agrement: {
              type: 'string',
              description: 'Agreement number',
            },
            approved: {
              type: 'boolean',
              description: 'Approval status',
            },
            createdAt: {
              type: 'string',
              format: 'date-time',
            },
            updatedAt: {
              type: 'string',
              format: 'date-time',
            },
          },
        },
        Production: {
          type: 'object',
          required: ['reference', 'produit', 'compte'],
          properties: {
            _id: {
              type: 'string',
              description: 'Production ID',
            },
            reference: {
              type: 'string',
              description: 'Unique reference number',
            },
            produit: {
              type: 'string',
              description: 'Product name',
            },
            poids: {
              type: 'string',
              description: 'Weight',
            },
            quantite: {
              type: 'string',
              description: 'Quantity',
            },
            origine: {
              type: 'string',
              description: 'Origin',
            },
            numLotProduction: {
              type: 'string',
              description: 'Production lot number',
            },
            dlc: {
              type: 'string',
              format: 'date',
              description: 'Expiry date',
            },
            dateDebut: {
              type: 'string',
              format: 'date',
              description: 'Start date',
            },
            dateFin: {
              type: 'string',
              format: 'date',
              description: 'End date',
            },
            approved: {
              type: 'boolean',
              description: 'Approval status',
            },
            compte: {
              type: 'string',
              description: 'Associated company ID',
            },
            controlleur: {
              type: 'string',
              description: 'Controller ID',
            },
            createdAt: {
              type: 'string',
              format: 'date-time',
            },
            updatedAt: {
              type: 'string',
              format: 'date-time',
            },
          },
        },
        Error: {
          type: 'object',
          properties: {
            message: {
              type: 'string',
              description: 'Error message',
            },
            stack: {
              type: 'string',
              description: 'Error stack trace (development only)',
            },
          },
        },
      },
    },
    security: [
      {
        bearerAuth: [],
      },
    ],
  },
  apis: ['./src/routes/*.js'], // paths to files containing OpenAPI definitions
};

const specs = swaggerJsdoc(options);

export const setupSwagger = (app) => {
  app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(specs, {
    explorer: true,
    customCss: '.swagger-ui .topbar { display: none }',
    customSiteTitle: 'CertiTrace API Documentation',
  }));
};

export default specs;
