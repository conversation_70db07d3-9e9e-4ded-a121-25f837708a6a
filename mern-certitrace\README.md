# CertiTrace - MERN Stack Application

A modern certification and traceability system for food production built with the MERN stack (MongoDB, Express.js, React, Node.js) and Vite.

## Project Structure

```
mern-certitrace/
├── backend/                 # Node.js + Express API
│   ├── src/
│   │   ├── controllers/     # API controllers
│   │   ├── models/         # MongoDB models (Mongoose)
│   │   ├── routes/         # API routes
│   │   ├── middleware/     # Custom middleware
│   │   ├── utils/          # Utility functions
│   │   └── config/         # Configuration files
│   ├── uploads/            # File uploads
│   └── package.json
├── frontend/               # React + Vite application
│   ├── src/
│   │   ├── components/     # React components
│   │   ├── pages/          # Page components
│   │   ├── hooks/          # Custom hooks
│   │   ├── services/       # API services
│   │   ├── store/          # State management
│   │   ├── utils/          # Utility functions
│   │   └── assets/         # Static assets
│   ├── public/             # Public assets
│   └── package.json
└── README.md
```

## Features

- **User Management**: Admin and Customer roles with JWT authentication
- **Company Management**: Company profiles with categories and contact information
- **Production Chain Tracking**: Complete traceability from production to logistics
  - Production (Fabrication)
  - Elevage (Breeding)
  - Sacrifice (Slaughter)
  - Decoupe (Cutting)
  - Fabrication (Manufacturing)
  - Conditionnement (Packaging)
  - Logistique (Logistics)
- **Certificate Generation**: PDF certificates for each production stage
- **Search Functionality**: Global search across all production data
- **File Import**: Excel file imports for bulk data entry
- **Real-time Updates**: Live updates using WebSocket connections

## Technology Stack

### Backend
- **Node.js** - Runtime environment
- **Express.js** - Web framework
- **MongoDB** - Database
- **Mongoose** - ODM for MongoDB
- **JWT** - Authentication
- **Multer** - File uploads
- **ExcelJS** - Excel file processing
- **PDFKit** - PDF generation

### Frontend
- **React 18** - UI library
- **Vite** - Build tool and dev server
- **React Router** - Client-side routing
- **Redux Toolkit** - State management
- **Axios** - HTTP client
- **Tailwind CSS** - Styling
- **React Hook Form** - Form handling
- **React Query** - Server state management

## Getting Started

### Prerequisites
- Node.js (v18 or higher)
- MongoDB (v5 or higher)
- npm or yarn

### Installation

1. Clone the repository
```bash
git clone <repository-url>
cd mern-certitrace
```

2. Install backend dependencies
```bash
cd backend
npm install
```

3. Install frontend dependencies
```bash
cd ../frontend
npm install
```

4. Set up environment variables
```bash
# Backend (.env)
cp backend/.env.example backend/.env
# Edit backend/.env with your configuration

# Frontend (.env)
cp frontend/.env.example frontend/.env
# Edit frontend/.env with your configuration
```

5. Start the development servers
```bash
# Terminal 1 - Backend
cd backend
npm run dev

# Terminal 2 - Frontend
cd frontend
npm run dev
```

## Development

### Backend Development
```bash
cd backend
npm run dev          # Start development server
npm run test         # Run tests
npm run build        # Build for production
```

### Frontend Development
```bash
cd frontend
npm run dev          # Start development server
npm run build        # Build for production
npm run preview      # Preview production build
npm run test         # Run tests
```

## API Documentation

The API documentation will be available at `http://localhost:5000/api-docs` when running the backend server.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.
