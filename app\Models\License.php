<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class License extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $fillable = [
        'compte_id',
        'date_creation',
        'date_expiration',
        'auditeur',
        'adresse_auditeur',
    ];


    public function compte(){
        return $this->belongsTo(Compte::class);
    }
}
