import { Routes, Route, Navigate } from 'react-router-dom'
import { useSelector } from 'react-redux'
import { useEffect } from 'react'

// Layouts
import AuthLayout from './components/layouts/AuthLayout'
import DashboardLayout from './components/layouts/DashboardLayout'
import PublicLayout from './components/layouts/PublicLayout'

// Pages
import LoginPage from './pages/auth/LoginPage'
import RegisterPage from './pages/auth/RegisterPage'
import ForgotPasswordPage from './pages/auth/ForgotPasswordPage'
import ResetPasswordPage from './pages/auth/ResetPasswordPage'

import HomePage from './pages/public/HomePage'
import SearchPage from './pages/public/SearchPage'
import CertificatePage from './pages/public/CertificatePage'

import AdminDashboard from './pages/admin/AdminDashboard'
import UserManagement from './pages/admin/UserManagement'
import CompanyManagement from './pages/admin/CompanyManagement'

import CustomerDashboard from './pages/customer/CustomerDashboard'
import ProfilePage from './pages/customer/ProfilePage'

import ProductionList from './pages/production/ProductionList'
import ProductionForm from './pages/production/ProductionForm'
import ProductionDetail from './pages/production/ProductionDetail'

import ElevageList from './pages/elevage/ElevageList'
import ElevageForm from './pages/elevage/ElevageForm'
import ElevageDetail from './pages/elevage/ElevageDetail'

import SacrificeList from './pages/sacrifice/SacrificeList'
import SacrificeForm from './pages/sacrifice/SacrificeForm'
import SacrificeDetail from './pages/sacrifice/SacrificeDetail'

import DecoupeList from './pages/decoupe/DecoupeList'
import DecoupeForm from './pages/decoupe/DecoupeForm'
import DecoupeDetail from './pages/decoupe/DecoupeDetail'

import FabricationList from './pages/fabrication/FabricationList'
import FabricationForm from './pages/fabrication/FabricationForm'
import FabricationDetail from './pages/fabrication/FabricationDetail'

import ConditionnementList from './pages/conditionnement/ConditionnementList'
import ConditionnementForm from './pages/conditionnement/ConditionnementForm'
import ConditionnementDetail from './pages/conditionnement/ConditionnementDetail'

import LogistiqueList from './pages/logistique/LogistiqueList'
import LogistiqueForm from './pages/logistique/LogistiqueForm'
import LogistiqueDetail from './pages/logistique/LogistiqueDetail'

import NotFoundPage from './pages/NotFoundPage'
import LoadingPage from './pages/LoadingPage'

// Components
import ProtectedRoute from './components/auth/ProtectedRoute'
import { useAuthCheck } from './hooks/useAuth'

function App() {
  const { isAuthenticated, user, isLoading } = useSelector((state) => state.auth)
  
  // Check authentication status on app load
  useAuthCheck()

  if (isLoading) {
    return <LoadingPage />
  }

  return (
    <div className="App">
      <Routes>
        {/* Public Routes */}
        <Route path="/" element={<PublicLayout />}>
          <Route index element={<HomePage />} />
          <Route path="search" element={<SearchPage />} />
          <Route path="certificate/:type/:id" element={<CertificatePage />} />
        </Route>

        {/* Auth Routes */}
        <Route path="/auth" element={<AuthLayout />}>
          <Route path="login" element={<LoginPage />} />
          <Route path="register" element={<RegisterPage />} />
          <Route path="forgot-password" element={<ForgotPasswordPage />} />
          <Route path="reset-password/:token" element={<ResetPasswordPage />} />
        </Route>

        {/* Protected Dashboard Routes */}
        <Route
          path="/dashboard"
          element={
            <ProtectedRoute>
              <DashboardLayout />
            </ProtectedRoute>
          }
        >
          {/* Admin Routes */}
          <Route
            path="admin"
            element={
              <ProtectedRoute requiredRole="admin">
                <AdminDashboard />
              </ProtectedRoute>
            }
          />
          <Route
            path="admin/users"
            element={
              <ProtectedRoute requiredRole="admin">
                <UserManagement />
              </ProtectedRoute>
            }
          />
          <Route
            path="admin/companies"
            element={
              <ProtectedRoute requiredRole="admin">
                <CompanyManagement />
              </ProtectedRoute>
            }
          />

          {/* Customer Routes */}
          <Route
            path="customer"
            element={
              <ProtectedRoute requiredRole="customer">
                <CustomerDashboard />
              </ProtectedRoute>
            }
          />
          <Route
            path="profile"
            element={
              <ProtectedRoute>
                <ProfilePage />
              </ProtectedRoute>
            }
          />

          {/* Production Chain Routes */}
          <Route path="productions">
            <Route index element={<ProductionList />} />
            <Route path="new" element={<ProductionForm />} />
            <Route path=":id" element={<ProductionDetail />} />
            <Route path=":id/edit" element={<ProductionForm />} />
          </Route>

          <Route path="elevages">
            <Route index element={<ElevageList />} />
            <Route path="new" element={<ElevageForm />} />
            <Route path=":id" element={<ElevageDetail />} />
            <Route path=":id/edit" element={<ElevageForm />} />
          </Route>

          <Route path="sacrifices">
            <Route index element={<SacrificeList />} />
            <Route path="new" element={<SacrificeForm />} />
            <Route path=":id" element={<SacrificeDetail />} />
            <Route path=":id/edit" element={<SacrificeForm />} />
          </Route>

          <Route path="decoupes">
            <Route index element={<DecoupeList />} />
            <Route path="new" element={<DecoupeForm />} />
            <Route path=":id" element={<DecoupeDetail />} />
            <Route path=":id/edit" element={<DecoupeForm />} />
          </Route>

          <Route path="fabrications">
            <Route index element={<FabricationList />} />
            <Route path="new" element={<FabricationForm />} />
            <Route path=":id" element={<FabricationDetail />} />
            <Route path=":id/edit" element={<FabricationForm />} />
          </Route>

          <Route path="conditionnements">
            <Route index element={<ConditionnementList />} />
            <Route path="new" element={<ConditionnementForm />} />
            <Route path=":id" element={<ConditionnementDetail />} />
            <Route path=":id/edit" element={<ConditionnementForm />} />
          </Route>

          <Route path="logistiques">
            <Route index element={<LogistiqueList />} />
            <Route path="new" element={<LogistiqueForm />} />
            <Route path=":id" element={<LogistiqueDetail />} />
            <Route path=":id/edit" element={<LogistiqueForm />} />
          </Route>

          {/* Default dashboard redirect */}
          <Route
            index
            element={
              <Navigate
                to={user?.role === 'admin' ? '/dashboard/admin' : '/dashboard/customer'}
                replace
              />
            }
          />
        </Route>

        {/* Redirect authenticated users from auth pages */}
        {isAuthenticated && (
          <Route
            path="/auth/*"
            element={
              <Navigate
                to={user?.role === 'admin' ? '/dashboard/admin' : '/dashboard/customer'}
                replace
              />
            }
          />
        )}

        {/* 404 Page */}
        <Route path="*" element={<NotFoundPage />} />
      </Routes>
    </div>
  )
}

export default App
