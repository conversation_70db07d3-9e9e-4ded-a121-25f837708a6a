<?php

namespace App\Http\Controllers\Customer;

use App\Http\Controllers\Controller;
use App\Models\Elevage;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class ElevageController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            $data = Elevage::where('compte_id',Auth::user()->compte_id)->get();
            return datatables()->of($data)
                ->addColumn('controlleur', function (Elevage $elevage) {
                    // Check if 'controlleur' relationship exists before accessing 'nom'
                    return $elevage->controlleur ? $elevage->controlleur->nom : '';
                })
                ->addColumn('compte_id', function (Elevage $elevage) {
                    // Check if 'producteur' relationship exists before accessing 'nom'
                    return $elevage->compte ? $elevage->compte->name : '';
                })
                ->addColumn('user_id', function (Elevage $elevage) {
                    // Check if 'producteur' relationship exists before accessing 'nom'
                    return $elevage->user ? $elevage->user->name : '';
                })
                ->addColumn('approved', function (Elevage $elevage) {
                    // Set badge text based on the value of 'approved'
                    $badge = $elevage->approved == 1 ? '<span class="badge badge-light-success">Approuvé</span>' : '<span class="badge badge-light-danger">Non Approuvé</span>';
                    return $badge;
                })
                ->addColumn('action', function (Elevage $elevage) {
                    // Generate action button HTML with route
                    return '<a href="'.route('elevage.show', $elevage->id).'" class="btn btn-sm btn-icon btn-bg-light btn-active-color-primary">
                <!--begin::Svg Icon | path: icons/duotune/arrows/arr064.svg-->
                <span class="svg-icon svg-icon-2">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <rect opacity="0.5" x="18" y="13" width="13" height="2" rx="1" transform="rotate(-180 18 13)" fill="currentColor"></rect>
                        <path d="M15.4343 12.5657L11.25 16.75C10.8358 17.1642 10.8358 17.8358 11.25 18.25C11.6642 18.6642 12.3358 18.6642 12.75 18.25L18.2929 12.7071C18.6834 12.3166 18.6834 11.6834 18.2929 11.2929L12.75 5.75C12.3358 5.33579 11.6642 5.33579 11.25 5.75C10.8358 6.16421 10.8358 6.83579 11.25 7.25L15.4343 11.4343C15.7467 11.7467 15.7467 12.2533 15.4343 12.5657Z" fill="currentColor"></path>
                    </svg>
                </span>
                <!--end::Svg Icon-->
                               </a>';
                })
                ->rawColumns(['approved','action'])
                ->toJson();
        }
        return view('customer.Elevage.index');
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        return view('customer.Elevage.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        // Get the current date in the format 'daymonthyear'
        $date = now()->format('dmY');

        // Generate a unique code (e.g., random string or incrementing number)
        $code = Str::random(4); // You can change this to generate your code

        // Combine the date and code to form the reference
        $reference = "E-{$date}{$code}";
        //dd($reference);
        // Create a new Production instance with the generated reference
        $record = new Elevage([
            'reference' => $reference, // Call the function using $this->generateReference()
            'controlleur_id' => $request->controlleur_id,
            'compte_id' => Auth::user()->compte_id,
            'organisme_control_id' => $request->organisme_control_id,
            'date_debut' => Carbon::createFromFormat('Y-m-d', $request->date_debut),
            'date_fin' => Carbon::createFromFormat('Y-m-d', $request->date_fin),
            'user_id' => Auth::user()->id,

            'produit' => $request->produit,
            'poids' => $request->poids,
            'quantite' => $request->quantite,
            'origine' => $request->origine,
            'num_lot_elevage' => $request->num_lot_elevage,
            'age' => $request->age,

        ]);

        // Save the production
        $record->save();
        return redirect()->route('elevage.show',$record->id);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $record = Elevage::find($id);
        return view('customer.Elevage.show',compact('record'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $record = Elevage::find($id);
        return view('customer.Elevage.edit',compact('record'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        // Valider la requête
        $request->validate([
            'image' => 'required|image|mimes:jpeg,png,jpg|max:2048', // Ajuster les règles de validation selon vos besoins
        ]);

        // Récupérer le produit
        $record = Elevage::findOrFail($id);

        // Enregistrer la nouvelle image
        if ($request->hasFile('image')) {
            $image = $request->file('image');
            $imageName = time() . '.' . $image->getClientOriginalExtension();
            $image->move(public_path('upload/elevage'), $imageName);

            // Mettre à jour le chemin de l'image du produit
            $record->image = 'upload/elevage/' . $imageName;
            $record->save();
        }

        return redirect()->back();
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}
