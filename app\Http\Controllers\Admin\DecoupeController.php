<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Decoupe;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class DecoupeController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        if ($request->ajax()) {
            $data = Decoupe::all();
            return datatables()->of($data)
                ->addColumn('controlleur_id', function (Decoupe $decoupe) {
                    // Check if 'controlleur' relationship exists before accessing 'nom'
                    return $decoupe->controlleur ? $decoupe->controlleur->nom : '';
                })
                ->addColumn('controlleur_id', function (Decoupe $decoupe) {
                    // Check if 'controlleur' relationship exists before accessing 'nom'
                    return $decoupe->controlleur ? $decoupe->controlleur->nom : '';
                })
                ->addColumn('compte', function (Decoupe $decoupe) {
                    // Check if 'controlleur' relationship exists before accessing 'nom'
                    return $decoupe->compte ? $decoupe->compte->name : '';
                })
                ->addColumn('approved', function (Decoupe $decoupe) {
                    // Set badge text based on the value of 'approved'
                    $badge = $decoupe->approved == 1 ? '<span class="badge badge-light-success">Approuvé</span>' : '<span class="badge badge-light-danger">Non Approuvé</span>';
                    return $badge;
                })
                ->addColumn('actions', function (Decoupe $decoupe) {
                    $showButton = '<a href="'.route('decoupes.show', $decoupe->id).'" class="btn btn-sm btn-icon btn-bg-light btn-active-color-primary">
        <span class="svg-icon svg-icon-2">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-eyeglasses" viewBox="0 0 16 16">
                <path d="M4 6a2 2 0 1 1 0 4 2 2 0 0 1 0-4m2.625.547a3 3 0 0 0-5.584.953H.5a.5.5 0 0 0 0 1h.541A3 3 0 0 0 7 8a1 1 0 0 1 2 0 3 3 0 0 0 5.959.5h.541a.5.5 0 0 0 0-1h-.541a3 3 0 0 0-5.584-.953A2 2 0 0 0 8 6c-.532 0-1.016.208-1.375.547M14 8a2 2 0 1 1-4 0 2 2 0 0 1 4 0"/>
            </svg>
        </span>
    </a>';

                    $duplicateButton = '<a href="'.route('decoupes.duplicate', $decoupe->id).'" class="btn btn-sm btn-icon btn-bg-light btn-active-color-primary">
        <span class="svg-icon svg-icon-2">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" class="bi bi-copy" viewBox="0 0 16 16">
                <path fill-rule="evenodd" d="M4 2a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2zm2-1a1 1 0 0 0-1 1v8a1 1 0 0 0 1 1h8a1 1 0 0 0 1-1V2a1 1 0 0 0-1-1zM2 5a1 1 0 0 0-1 1v8a1 1 0 0 0 1 1h8a1 1 0 0 0 1-1v-1h1v1a2 2 0 0 1-2 2H2a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h1v1z"/>
            </svg>
        </span>
    </a>';

                    return $showButton . ' ' . $duplicateButton;
                })
                ->rawColumns(['approved','actions'])
                ->toJson();
        }
        return view('admin.Decoupe.index');
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        return view('admin.Decoupe.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        // Get the current date in the format 'daymonthyear'
        $date = now()->format('dmY');

        // Generate a unique code (e.g., random string or incrementing number)
        $code = Str::random(4); // You can change this to generate your code

        // Combine the date and code to form the reference
        $reference = "D-{$date}{$code}";
        //dd($reference);
        // Create a new Production instance with the generated reference
        $record = new Decoupe([
            'reference' => $reference,
            'controlleur_id' => $request->controlleur_id,
            'compte_id' => $request->compte_id,
            'organisme_control_id' => $request->organisme_control_id,
            'societe_nettoyage_id' => $request->societe_nettoyage_id,
            'user_id' => Auth::user()->id,
            'code_produit' => $request->code_produit,
            'produit' => $request->produit,
            'poids' => $request->poids,
            'num_lot_sacrifice_id' => $request->num_lot_sacrifice_id,
            'num_lot_decoupe' => $request->num_lot_decoupe,
            'dlc' => Carbon::createFromFormat('Y-m-d', $request->dlc),

        ]);

        // Save the production
        $record->save();
        return redirect()->route('decoupes.show',$record->id);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $record = Decoupe::find($id);
        return view('admin.Decoupe.show',compact('record'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $record = Decoupe::find($id);
        return view('admin.Decoupe.edit',compact('record'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {


        // Récupérer le produit
        $record = Decoupe::findOrFail($id);
        //$record->reference = $request->reference;
        $record->controlleur_id = $request->controlleur_id;
        $record->compte_id = $request->compte_id;
        $record->organisme_control_id = $request->organisme_control_id;
        $record->societe_nettoyage_id = $request->societe_nettoyage_id;
        //$record->user_id = Auth::user()->id;
        $record->code_produit = $request->code_produit;
        $record->produit = $request->produit;
        $record->poids = $request->poids;
        $record->num_lot_sacrifice_id = $request->num_lot_sacrifice_id;
        $record->num_lot_decoupe = $request->num_lot_decoupe;
        $record->dlc = Carbon::createFromFormat('Y-m-d', $request->dlc);
        if ($request->hasFile('image')) {
            $image = $request->file('image');
            $imageName = time() . '.' . $image->getClientOriginalExtension();
            $image->move(public_path('upload/decoupe'), $imageName);

            // Mettre à jour le chemin de l'image du produit
            $record->image = 'upload/decoupe/' . $imageName;
        }
        $record->save();

        return redirect()->route('decoupes.show',$record->id);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        try {
            // Trouver l'objet à supprimer dans la base de données
            $controlleur = Decoupe::findOrFail($id);

            // Supprimer l'objet
            $controlleur->delete();

            return redirect()->route('decoupes.index');

        } catch (\Exception $e) {
            // Retourner une réponse d'erreur si une exception est levée
            return response()->json(['error' => 'Erreur lors de la suppression du contrôleur : ' . $e->getMessage()], 500);
        }
    }


    public function toggleApproved($id)
    {
        $production = Decoupe::findOrFail($id);
        $production->approved = !$production->approved;
        $production->save();

        return response()->json(['success' => true, 'approved' => $production->approved]);
    }


    public function showCertificat($id)
    {
        $record = Decoupe::find($id);
        return view('admin.Decoupe.print',compact('record'));
    }

    public function duplicate($id)
    {
        // Find the original decoupe record
        $originalDecoupe = Decoupe::find($id);

        if (!$originalDecoupe) {
            return redirect()->route('decoupes.index')->with('error', 'Decoupe not found.');
        }

        // Duplicate the decoupe record
        $newDecoupe = $originalDecoupe->replicate();

        // Generate new reference
        $date = now()->format('dmY');
        $code = Str::random(4); // You can change this to generate your code
        $reference = "D-{$date}{$code}";

        // Assign the new reference to the duplicated record
        $newDecoupe->reference = $reference;
        $newDecoupe->approved = 0;

        // Save the duplicated record
        $newDecoupe->save();

        return redirect()->route('decoupes.index')->with('success', 'Decoupe duplicated successfully.');
    }

}
